#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只添加公式到已写入的数据
"""

from openpyxl import load_workbook
import shutil
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

TARGET_FILE_PATH = r"juadge_data_deal\Data\deal_data\广告数据对比总表.xlsx"
TARGET_SHEET_NAME = "台账"

def add_formulas(start_row, end_row):
    """
    为指定行范围添加计算公式
    """
    logger.info(f"开始为行 {start_row} 到 {end_row} 添加公式...")
    
    try:
        # 创建备份
        backup_path = TARGET_FILE_PATH.replace('.xlsx', f'_backup_before_formulas_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
        shutil.copy2(TARGET_FILE_PATH, backup_path)
        logger.info(f"已创建备份文件: {backup_path}")
        
        wb = load_workbook(TARGET_FILE_PATH)
        ws = wb[TARGET_SHEET_NAME]
        
        # 公式模板（根据实际列位置调整）
        # 列位置：A=广告账户, B=时间, C=广告系列, D=广告组, E=广告名称, F=短剧编号, G=展示次数, H=千次展示费用, I=点击量, J=调整决策, K=成效, L=点击注册转化率, M=展示注册转化率, N=点击率, O=单次注册成本, P=已花费金额
        
        formula_templates = {
            'H': '=IF(G{row}>0,P{row}/G{row}*1000,"")',  # 千次展示费用 = 已花费金额/展示次数*1000
            'L': '=IF(I{row}>0,K{row}/I{row}*100,"")',   # 点击注册转化率 = 成效/点击量*100%
            'M': '=IF(G{row}>0,K{row}/G{row}*100,"")',   # 展示注册转化率 = 成效/展示次数*100%
            'N': '=IF(G{row}>0,I{row}/G{row}*100,"")',   # 点击率 = 点击量/展示次数*100%
            'O': '=IF(K{row}>0,P{row}/K{row},"")'        # 单次注册成本 = 已花费金额/成效
        }
        
        # 为每一行添加公式
        formulas_added = 0
        for row_num in range(start_row, end_row + 1):
            for col, formula_template in formula_templates.items():
                if formula_template:  # 只有非空的公式模板才添加
                    formula = formula_template.format(row=row_num)
                    ws[f'{col}{row_num}'] = formula
                    formulas_added += 1
        
        wb.save(TARGET_FILE_PATH)
        logger.info(f"公式添加完成，共添加 {formulas_added} 个公式")
        return True
        
    except Exception as e:
        logger.error(f"添加公式时出错: {e}")
        return False

def main():
    """
    主函数
    """
    logger.info("开始添加公式...")
    
    # 为第1277-1288行添加公式
    start_row = 1277
    end_row = 1288
    
    if add_formulas(start_row, end_row):
        logger.info("公式添加成功！")
    else:
        logger.error("公式添加失败！")

if __name__ == "__main__":
    main()
