# 广告数据分析器 (AdDataAnalyzer)

一个专业的广告数据处理和分析工具，用于整合多个Excel文件中的广告数据，生成统一的数据对比总表。

## 🚀 功能特点

### 核心功能
- **智能数据合并**：自动识别和合并多个Excel文件中的广告数据
- **重复数据处理**：智能检测重复记录，更新现有数据而非重复添加
- **列映射识别**：自动识别不同格式的Excel文件中的数据列
- **数据验证**：确保金额字段显示正确的数值而非货币单位标识
- **唯一广告名称管理**：自动维护唯一广告名称列表，避免重复

### 数据处理特性
- **自动排序**：按广告账户从小到大自动排序
- **公式生成**：自动生成计算列的Excel公式
- **数据清洗**：过滤空行和无效数据
- **格式标准化**：统一日期格式和数值格式

## 📁 项目结构

```
AdDataAnalyzer/
├── README.md                           # 项目说明文档
├── requirements.txt                    # Python依赖包列表
├── quick_start.bat                     # 快速启动脚本
├── install_dependencies.bat            # 依赖安装脚本
├── combine_to_xlsx.bat                 # 数据合并脚本
├── ad_data_merger.py                   # 🆕 新版数据合并脚本
├── add_formulas_only.py                # 🆕 公式添加脚本
├── ad_data_merger.log                  # 🆕 脚本运行日志
├── juadge_data_deal/                   # 主要代码目录
│   ├── run.py                          # 应用程序入口
│   ├── Data/                           # 数据目录
│   │   ├── origin_data/                # 原始Excel文件存放目录
│   │   │   ├── daily_analysis.xlsx     # 示例原始数据文件
│   │   │   └── daily_analysis(1).xlsx  # 示例原始数据文件
│   │   └── deal_data/                  # 处理后数据存放目录
│   │       └── 广告数据对比总表.xlsx    # 目标数据文件
│   └── Script/                         # 脚本目录
│       ├── gui/                        # 图形界面相关
│       │   ├── analysis_xlsx.py        # Streamlit分析界面
│       │   └── combine_to_xlsx.py      # PyQt6合并界面
│       └── deal_data/                  # 数据处理核心
│           ├── xlsxgroup_to_sheet-v3.py # 核心数据处理逻辑
│           └── test_xlsxgroup_fix.py   # 单元测试
├── lib/                                # 本地Python库
└── version/                            # 版本历史
```

## 🛠️ 安装和使用

### 环境要求
- Python 3.9+
- Windows 操作系统
- Excel 2016+ (用于查看结果文件)

### 快速开始

1. **安装依赖**
   ```bash
   # 运行依赖安装脚本
   install_dependencies.bat
   
   # 或手动安装
   pip install -r requirements.txt
   ```

2. **准备数据**
   - 将需要处理的Excel文件放入 `juadge_data_deal/Data/origin_data/` 目录
   - 支持的文件格式：`.xlsx`

3. **启动应用**
   ```bash
   # 方式1：使用新版数据合并脚本 (推荐)
   python ad_data_merger.py

   # 方式2：使用快速启动脚本
   quick_start.bat

   # 方式3：直接运行Python程序
   python juadge_data_deal/run.py

   # 方式4：使用数据合并工具
   combine_to_xlsx.bat
   ```

### 使用方式

#### 1. 新版数据合并脚本 (推荐) 🆕
- 运行 `python ad_data_merger.py`
- 自动处理所有原始数据文件
- 智能定位数据插入位置
- 自动填充计算公式
- 详细的日志输出和错误处理

**特点：**
- ✅ 精确的数据位置控制
- ✅ 完整的公式自动填充
- ✅ 智能的账户名称和日期格式转换
- ✅ 自动备份和错误恢复

#### 2. Web界面 (Streamlit)
- 运行 `quick_start.bat` 或 `python juadge_data_deal/run.py`
- 浏览器会自动打开分析界面
- 上传Excel文件进行分析

#### 3. 桌面应用 (PyQt6)
- 运行 `combine_to_xlsx.bat`
- 使用图形界面选择源目录和目标文件
- 点击"开始处理"进行数据合并

#### 4. 命令行使用
```python
from juadge_data_deal.Script.deal_data.xlsxgroup_to_sheet_v3 import process_files_to_excel

# 处理数据
source_dir = "path/to/excel/files"
target_excel = "path/to/output.xlsx"
success = process_files_to_excel(source_dir, target_excel)
```

## 📊 数据格式说明

### 输入数据格式
支持包含以下字段的Excel文件：
- **广告账户**：广告账户ID或名称
- **时间**：报告日期 (支持多种日期格式)
- **广告名称**：广告的名称标识
- **展示次数**：广告展示的次数
- **点击量**：广告被点击的次数
- **成效**：转化或注册数量
- **已花费金额**：广告花费金额 (自动识别数值)

### 输出数据格式
生成的Excel文件包含以下列：
- 基础数据列：广告账户、时间、广告名称、展示次数、点击量、成效、已花费金额
- 计算列：千次展示费用、点击率、转化率、单次注册成本等
- 关联列：广告系列、广告组、短剧编号 (通过VLOOKUP公式关联)

## ⚙️ 核心参数配置

### 重要配置项
- **源数据目录**：`juadge_data_deal/Data/origin_data/`
- **输出文件**：`juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx`
- **工作表名称**：`台账` (主数据表), `唯一广告名称` (广告名称索引表)

### 数据处理参数
- **重复检测字段**：广告账户 + 时间 + 广告名称
- **排序规则**：按广告账户数值从小到大排序
- **日期格式**：统一转换为 `yyyy/m/d` 格式
- **数值精度**：保持原始精度，自动识别数值类型

## 🔧 高级功能

### 重复数据处理
- 自动检测基于关键字段的重复记录
- 更新现有数据而非创建重复行
- 保持数据的最新状态

### 智能列映射
- 自动识别不同命名规则的数据列
- 支持 `Unnamed` 列的智能匹配
- 数值列大小分析和自动分类

### 错误处理
- 完整的异常处理机制
- 详细的处理日志输出
- 自动备份原始文件

## 🧪 测试

运行单元测试：
```bash
# 运行所有测试
python -m pytest juadge_data_deal/Script/deal_data/test_xlsxgroup_fix.py -v

# 运行特定测试
python -m pytest juadge_data_deal/Script/deal_data/test_xlsxgroup_fix.py::TestUSDColumnFix::test_duplicate_detection_functionality -v
```

## 📝 更新日志

### v4.0 (最新版本) 🆕 2025-01-01
- ✅ **新增专用数据合并脚本** (`ad_data_merger.py`)
  - 精确的数据位置控制，正确追加到最后一行有数据的位置
  - 完整的列对应关系映射，按目标工作表列顺序写入
  - 智能的账户名称格式转换 (Mioeye--06 → 6)
  - 标准化的日期格式转换 (2025-07-31 → 2025/7/31)
- ✅ **自动公式填充功能**
  - 千次展示费用、点击率、转化率等关键指标自动计算
  - 包含IF条件判断，避免除零错误
- ✅ **完善的错误处理和日志系统**
  - 详细的处理进度和结果统计
  - 自动备份机制，防止数据丢失
  - 完整的异常处理和恢复机制

### v3.0
- ✅ 修复USD列识别问题，确保金额字段显示正确数值
- ✅ 新增重复数据检测和更新功能
- ✅ 优化唯一广告名称管理
- ✅ 增强错误处理和日志输出
- ✅ 完善单元测试覆盖

### v2.0
- 添加PyQt6图形界面
- 改进数据处理逻辑
- 增加自动排序功能

### v1.0
- 基础数据合并功能
- Streamlit Web界面
- 基本的Excel处理能力

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持与反馈

如有问题或建议，请：
1. 提交 [Issue](../../issues)
2. 发送邮件至项目维护者
3. 查看项目文档和FAQ

---

**注意**：本工具专为广告数据分析设计，请确保输入数据格式符合要求以获得最佳处理效果。
