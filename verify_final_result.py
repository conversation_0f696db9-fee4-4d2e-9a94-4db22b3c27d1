#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证最终写入结果
"""

from openpyxl import load_workbook

# 加载工作簿
wb = load_workbook('juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx')
ws = wb['台账']

print("=== 验证最终写入结果 ===")

# 找到真正的最后一行有数据的位置
last_data_row = 1
for row in range(2000, 0, -1):  # 从第2000行往前检查
    # 检查关键列：A列（广告账户）和E列（广告名称）
    a_value = ws.cell(row, 1).value  # A列
    e_value = ws.cell(row, 5).value  # E列
    
    if (a_value is not None and str(a_value).strip() != '') or \
       (e_value is not None and str(e_value).strip() != ''):
        last_data_row = row
        break

print(f"当前最后一行有数据：第{last_data_row}行")

# 显示最后几行的数据
print(f"\n最后5行的数据（第{last_data_row-4}行到第{last_data_row}行）：")
for row in range(max(1, last_data_row-4), last_data_row+1):
    a_value = ws.cell(row, 1).value  # 广告账户
    b_value = ws.cell(row, 2).value  # 时间
    e_value = ws.cell(row, 5).value  # 广告名称
    g_value = ws.cell(row, 7).value  # 展示次数
    
    print(f"第{row}行: A={a_value}, B={b_value}, E={e_value}, G={g_value}")

# 检查第1277-1288行（应该是新写入的数据）
print(f"\n检查第1277-1288行（新写入的数据）：")
for row in range(1277, 1289):
    a_value = ws.cell(row, 1).value  # 广告账户
    b_value = ws.cell(row, 2).value  # 时间
    e_value = ws.cell(row, 5).value  # 广告名称
    g_value = ws.cell(row, 7).value  # 展示次数
    
    if any(val is not None for val in [a_value, b_value, e_value, g_value]):
        print(f"第{row}行: A={a_value}, B={b_value}, E={e_value}, G={g_value}")

print("\n=== 验证完成 ===")
