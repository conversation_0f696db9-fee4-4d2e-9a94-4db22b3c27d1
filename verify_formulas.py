#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证公式是否正确添加
"""

from openpyxl import load_workbook

# 加载工作簿
wb = load_workbook('juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx')
ws = wb['台账']

print("=== 验证公式添加结果 ===")

print(f"\n检查第1277-1280行的公式（前4行作为示例）:")
for row_num in range(1277, 1281):
    print(f"\n第{row_num}行:")
    
    # 检查基础数据
    a_value = ws.cell(row_num, 1).value  # 广告账户
    g_value = ws.cell(row_num, 7).value  # 展示次数
    i_value = ws.cell(row_num, 9).value  # 点击量
    k_value = ws.cell(row_num, 11).value  # 成效
    p_value = ws.cell(row_num, 16).value  # 已花费金额
    
    print(f"  基础数据: A={a_value}, G={g_value}, I={i_value}, K={k_value}, P={p_value}")
    
    # 检查公式列
    h_cell = ws.cell(row_num, 8)  # 千次展示费用
    l_cell = ws.cell(row_num, 12)  # 点击注册转化率
    m_cell = ws.cell(row_num, 13)  # 展示注册转化率
    n_cell = ws.cell(row_num, 14)  # 点击率
    o_cell = ws.cell(row_num, 15)  # 单次注册成本
    
    print(f"  H列公式: {h_cell.value}")
    print(f"  L列公式: {l_cell.value}")
    print(f"  M列公式: {m_cell.value}")
    print(f"  N列公式: {n_cell.value}")
    print(f"  O列公式: {o_cell.value}")

print("\n=== 验证完成 ===")
