# 测试已花费金额范围修正
import pandas as pd
import sys
import os

# 添加脚本路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的函数
import importlib.util
script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "xlsxgroup_to_sheet-v3.py")
spec = importlib.util.spec_from_file_location("xlsxgroup_to_sheet_v3", script_path)
xlsxgroup_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(xlsxgroup_module)

# 从模块中导入函数
analyze_numeric_columns = xlsxgroup_module.analyze_numeric_columns
smart_numeric_mapping = xlsxgroup_module.smart_numeric_mapping

def test_cost_range_fix():
    """
    测试已花费金额范围修正 (0-15)
    """
    print("=== 测试已花费金额范围修正 ===")
    
    # 模拟实际Excel文件的数据结构，包含0-15范围的费用数据
    test_data = pd.DataFrame({
        'Unnamed: 0': ['AS01/250730', 'AS01/250730', 'AS01/250730', 'AS02/250730', 'AS03/250730'],
        'Unnamed: 1': ['AS01/AG01/AQ/250730', 'AS01/AG01/AQ/250730', 'AS01/AG01/AQ/250730', 'AS02/AG02/AQ/250730', 'AS03/AG03/AQ/250730'],
        'Unnamed: 2': [
            'AS01/AG01/AQ/AD01/PH/TV/250730/1%MZ/343', 
            'AS01/AG01/AQ/AD02/PH/TV/250730/1%MZ/344', 
            'AS01/AG01/AQ/AD03/PH/TV/250730/1%MZ/345',
            'AS02/AG02/AQ/AD04/PH/TV/250730/1%MZ/346',
            'AS03/AG03/AQ/AD05/PH/TV/250730/1%MZ/347'
        ],
        'Unnamed: 3': ['Mioeye--05', 'Mioeye--04', 'Mioeye--04', 'Mioeye--03', 'Mioeye--01'],
        1968: [248, 280, 260, 290, 250],  # 展示次数 - 最大数值
        263: [51, 40, 30, 45, 25],        # 点击量 - 第二大数值
        7: [5, 3, 1, 4, 2],               # 成效 - 小数值
        'USD': ['USD', 'USD', 'USD', 'USD', 'USD'],
        3.27: [15.5, 20.0, 18.2, 22.1, 16.8],  # 超出0-15范围的数值
        0.46714286: [0.42, 0.50, 0.38, 0.55, 0.45],  # 已花费金额 - 0-15范围内有小数
        13.36382114: [25.1, 30.5, 28.8, 35.2, 27.5],  # 超出0-15范围的数值
        'Unnamed: 11': ['active', 'active', 'active', 'active', 'active'],
        '2025-07-31': ['2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31'],
        '2025-07-31.1': ['2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31', '2025-07-31']
    })
    
    print("测试数据:")
    print(test_data.head())
    print(f"列名: {test_data.columns.tolist()}")
    
    # 测试数值列分析
    print("\n=== 数值列详细分析 ===")
    numeric_cols = analyze_numeric_columns(test_data)
    
    print("\n数值列排序结果:")
    for i, col_info in enumerate(numeric_cols):
        print(f"{i+1}. {col_info['column']}: 平均={col_info['mean']:.2f}, 范围={col_info['range']:.2f}, 有小数={col_info['has_decimals']}")
    
    # 测试已花费金额的智能映射
    print("\n=== 已花费金额映射测试 ===")
    result = smart_numeric_mapping('已花费金额 (USD)', numeric_cols)
    print(f"映射结果: {result}")
    
    # 验证结果
    if result == 0.46714286:
        print("✅ 成功！已花费金额正确映射到0-15范围内有小数的列")
        return True
    else:
        print(f"❌ 失败！期望映射到 0.46714286，实际映射到 {result}")
        return False

if __name__ == "__main__":
    try:
        print("开始测试已花费金额范围修正...")
        
        success = test_cost_range_fix()
        
        print("\n=== 测试完成 ===")
        if success:
            print("✅ 已花费金额范围修正测试通过")
        else:
            print("❌ 已花费金额范围修正测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
