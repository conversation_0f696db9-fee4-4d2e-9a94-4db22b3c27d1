#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有列的数据
"""

from openpyxl import load_workbook

# 加载工作簿
wb = load_workbook('juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx')
ws = wb['台账']

print("=== 检查新写入数据的所有列 ===")

# 显示列标题
print("列标题:")
headers = []
for i in range(1, 17):  # 显示前16列
    headers.append(f"{chr(64+i)}:{ws.cell(1, i).value}")
print(headers)

print(f"\n新写入的数据（第1277-1280行，前4行作为示例）:")
for row_num in range(1277, 1281):
    print(f"\n第{row_num}行:")
    for col_num in range(1, 17):  # 显示前16列
        cell_value = ws.cell(row_num, col_num).value
        col_letter = chr(64+col_num)
        print(f"  {col_letter}:{cell_value}")

print("\n=== 检查完成 ===")
