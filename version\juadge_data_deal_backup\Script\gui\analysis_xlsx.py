import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt
import seaborn as sns
import os
import numpy as np
import matplotlib
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import tempfile
from datetime import datetime, timedelta
import re
import io
import unicodedata
import json

# 设置页面标题和布局 - 必须是第一个Streamlit命令
st.set_page_config(page_title="广告数据分析", layout="wide", initial_sidebar_state="expanded")

# 设置中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'DejaVu Sans']  # 优先使用这些字体
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 函数：清理文件名，移除特殊字符
def clean_filename(filename):
    # 将中文字符转换为拉丁字符（如果可能）
    filename = unicodedata.normalize('NFKD', filename).encode('ascii', 'ignore').decode('ascii')
    # 移除所有非字母数字字符
    filename = re.sub(r'[^\w\.-]', '_', filename)
    # 确保文件名不为空
    if not filename or filename.startswith('.'):
        filename = "uploaded_file"
    return filename

# 添加与read_remarks.py完全相同的函数逻辑
# 修改display_remarks函数，使用更新后的get_remarks_dict函数
def display_remarks(df, uploaded_file=None, default_file_path=None):
    """显示Excel文件中的备注列数据，支持从上传的文件或默认文件中读取"""
    st.markdown('<p class="big-font" style="text-align: center;">备注数据</p>', unsafe_allow_html=True)
    
    try:
        # 使用更新后的get_remarks_dict函数获取备注
        remarks_dict = get_remarks_dict(uploaded_file=uploaded_file, excel_file_path=default_file_path)
        
        if not remarks_dict:
            st.warning("未找到任何备注数据。请确保Excel文件中包含'台账'表和'备注'列。")
            return
        
        # 创建一个新的列表来存储备注数据
        remarks_data = []
        
        # 首先收集所有带日期的广告名称
        ads_with_dates = set()
        for key in remarks_dict.keys():
            if "_" in key:
                ad_name = key.split("_", 1)[0]
                ads_with_dates.add(ad_name)
        
        # 解析每个键，提取广告名称和日期信息
        for key, remark in remarks_dict.items():
            if "_" in key:  # 复合键（广告名称_日期）
                parts = key.split("_", 1)
                ad_name = parts[0]
                date_str = parts[1] if len(parts) > 1 else ""
                remarks_data.append({"广告名称": str(ad_name), "日期": str(date_str), "备注": str(remark)})
            else:  # 仅广告名称
                # 如果该广告名称没有与日期组合的记录，则添加
                if key not in ads_with_dates:
                    remarks_data.append({"广告名称": str(key), "日期": "", "备注": str(remark)})
        
        # 创建DataFrame
        if remarks_data:
            remarks_df = pd.DataFrame(remarks_data)
            # 添加行号
            remarks_df.insert(0, "行号", range(1, len(remarks_df) + 1))
            
            # 明确设置列的数据类型
            remarks_df["广告名称"] = remarks_df["广告名称"].astype(str)
            remarks_df["日期"] = remarks_df["日期"].astype(str)
            remarks_df["备注"] = remarks_df["备注"].astype(str)
            
            # 显示统计信息
            st.success(f"共找到 {len(remarks_df)} 条非空备注")
            
            # 使用expander展示完整数据表
            with st.expander("查看完整备注数据表", expanded=True):
                st.dataframe(remarks_df, use_container_width=True)
            
            # 添加下载功能
            csv_data = remarks_df.to_csv(index=False, encoding='utf-8-sig')
            st.download_button(
                label="下载备注数据CSV",
                data=csv_data,
                file_name=f"备注数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
        else:
            st.warning("备注数据解析后为空，请检查数据格式")
    
    except Exception as e:
        st.error(f"处理备注数据时出错: {str(e)}")
        import traceback
        st.code(traceback.format_exc())

# 在文件开头的函数部分添加一个获取备注的辅助函数
# 修改get_remarks_dict函数，支持从上传的文件读取备注
def get_remarks_dict(uploaded_file=None, excel_file_path=None):
    """获取备注数据字典，键为广告名称和日期的组合，值为对应的备注
    
    参数:
        uploaded_file: 用户上传的文件对象
        excel_file_path: 文件路径字符串
    """
    try:
        # 如果有上传的文件，优先使用上传的文件
        if uploaded_file is not None:
            # 从上传的文件读取台账表
            try:
                # 使用BytesIO读取上传文件内容
                df_account = pd.read_excel(io.BytesIO(uploaded_file.getvalue()), sheet_name='台账')
            except Exception:
                # 如果没有台账表，尝试读取第一个表
                excel_file = pd.ExcelFile(io.BytesIO(uploaded_file.getvalue()))
                sheet_name = excel_file.sheet_names[0]
                df_account = pd.read_excel(io.BytesIO(uploaded_file.getvalue()), sheet_name=sheet_name)
        
        # 如果没有上传文件或读取失败，尝试从文件路径读取
        elif excel_file_path is not None and os.path.exists(excel_file_path):
            # 从指定路径读取台账表
            try:
                df_account = pd.read_excel(excel_file_path, sheet_name='台账')
            except Exception:
                # 如果没有台账表，尝试读取第一个表
                excel_file = pd.ExcelFile(excel_file_path)
                sheet_name = excel_file.sheet_names[0]
                df_account = pd.read_excel(excel_file_path, sheet_name=sheet_name)
        
        # 如果没有指定文件，尝试从默认路径读取
        else:
            # 查找可能的文件路径
            possible_paths = [
                "../../Data/deal_data/广告数据对比总表.xlsx",
                "../Data/deal_data/广告数据对比总表.xlsx",
                "./Data/deal_data/广告数据对比总表.xlsx",
                "Data/deal_data/广告数据对比总表.xlsx",
                "../../../Data/deal_data/广告数据对比总表.xlsx",
                "广告数据对比总表.xlsx"  # 当前目录
            ]
            
            file_found = False
            for path in possible_paths:
                if os.path.exists(path):
                    try:
                        df_account = pd.read_excel(path, sheet_name='台账')
                        file_found = True
                        break
                    except Exception:
                        # 如果没有台账表，尝试读取第一个表
                        try:
                            excel_file = pd.ExcelFile(path)
                            sheet_name = excel_file.sheet_names[0]
                            df_account = pd.read_excel(path, sheet_name=sheet_name)
                            file_found = True
                            break
                        except Exception:
                            continue
            
            if not file_found:
                return {}
        
        # 检查是否有备注列
        remarks_columns = [col for col in df_account.columns if '备注' in str(col)]
        if not remarks_columns:
            return {}
        
        remarks_column = remarks_columns[0]
        
        # 查找广告名称列
        ad_name_columns = [col for col in df_account.columns if '广告名称' in str(col)]
        if not ad_name_columns:
            # 如果没有明确的广告名称列，使用第一列
            ad_name_column = df_account.columns[0]
        else:
            ad_name_column = ad_name_columns[0]
        
        # 查找日期/时间列
        date_columns = [col for col in df_account.columns if any(keyword in str(col).lower() for keyword in ['日期', 'date', '时间', 'time'])]
        date_column = None
        if date_columns:
            date_column = date_columns[0]
        
        # 过滤非空备注
        df_with_remarks = df_account[df_account[remarks_column].notna()]
        
        # 创建更精确的备注字典
        remarks_dict = {}
        
        for _, row in df_with_remarks.iterrows():
            ad_name = str(row[ad_name_column])
            remark = str(row[remarks_column])
            
            # 创建键 - 如果有日期，则使用广告名称+日期，否则只使用广告名称
            if date_column and pd.notna(row.get(date_column)):
                date_val = row[date_column]
                # 尝试格式化日期
                if isinstance(date_val, (datetime, pd.Timestamp)):
                    date_str = date_val.strftime('%Y/%m/%d')
                else:
                    date_str = str(date_val)
                
                key = f"{ad_name}_{date_str}"
            else:
                key = ad_name
            
            remarks_dict[key] = remark
            
            # 同时存储一个仅使用广告名称的版本，作为后备
            if ad_name not in remarks_dict:
                remarks_dict[ad_name] = remark
        
        return remarks_dict
    except Exception as e:
        print(f"获取备注时出错: {str(e)}")
        return {}

# 函数：创建雷达图
def create_radar_chart(filtered_df, selected_ads, ad_name_column, metrics_columns,
                      chart_height, chart_bg_color, chart_text_color):
    """创建雷达图进行综合分析"""
    try:
        # 获取当前可见的广告列表（从session state中）
        visible_ads = [ad for ad in selected_ads if st.session_state.chart_visibility.get(ad, True)]

        # 准备雷达图数据
        metrics_list = list(metrics_columns.values())
        metrics_names = list(metrics_columns.keys())

        # 计算每个指标的最大值，用于标准化
        max_values = {}
        for metric in metrics_list:
            max_val = filtered_df[metric].max()
            max_values[metric] = max_val if pd.notnull(max_val) and max_val > 0 else 1

        # 创建Plotly雷达图
        fig = go.Figure()

        # 绘制每个可见广告的雷达图
        for ad in visible_ads:
            ad_data = filtered_df[filtered_df[ad_name_column] == ad].copy()

            if not ad_data.empty:
                # 计算平均值
                values = []
                hover_text = []
                display_text = []
                for metric, metric_name in zip(metrics_list, metrics_names):
                    avg_value = ad_data[metric].mean()
                    if pd.isna(avg_value):
                        avg_value = 0

                    # 保存原始值用于悬停显示
                    if '成本' in metric_name or '金额' in metric_name:
                        hover_text.append(f"{metric_name}: ¥{avg_value:.2f}")
                        display_text.append(f"¥{avg_value:.2f}")
                    elif '率' in metric_name:
                        hover_text.append(f"{metric_name}: {avg_value:.2%}")
                        display_text.append(f"{avg_value:.2%}")
                    else:
                        hover_text.append(f"{metric_name}: {avg_value:.2f}")
                        display_text.append(f"{avg_value:.2f}")

                    # 标准化处理（0-1范围）
                    if '成本' in metric_name or '花费' in metric_name:
                        # 成本指标：值越小越好，所以用1-标准化值
                        standardized = 1 - (avg_value / max_values[metric]) if max_values[metric] > 0 else 0
                    else:
                        # 其他指标：值越大越好
                        standardized = avg_value / max_values[metric] if max_values[metric] > 0 else 0
                    values.append(standardized)

                # 闭合雷达图
                values.append(values[0])
                metrics_names_closed = metrics_names + [metrics_names[0]]
                hover_text.append(hover_text[0])
                display_text.append(display_text[0])

                fig.add_trace(go.Scatterpolar(
                    r=values,
                    theta=metrics_names_closed,
                    fill='toself',
                    name=ad,
                    text=display_text,
                    textposition='top center',
                    textfont=dict(size=14, color=chart_text_color),
                    hovertext=hover_text,
                    hoverinfo='text+name'
                ))

        # 设置雷达图布局
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1],
                    tickfont=dict(size=14, color=chart_text_color),
                    tickvals=[0, 0.5, 1],
                    ticktext=["0%", "50%", "100%"]
                ),
                bgcolor=chart_bg_color
            ),
            title=dict(
                text='广告综合表现对比（标准化值）',
                font=dict(size=20, color=chart_text_color)
            ),
            showlegend=True,
            legend=dict(
                font=dict(size=14, color=chart_text_color)
            ),
            height=chart_height + 100,
            plot_bgcolor=chart_bg_color,
            paper_bgcolor=chart_bg_color,
            font=dict(color=chart_text_color)
        )

        # 显示交互式雷达图
        st.plotly_chart(fig, use_container_width=True)

        # 添加雷达图说明
        with st.expander("📖 雷达图说明"):
            st.markdown("""
            **雷达图解读指南：**
            - 🎯 **图形含义**：每个广告形成一个多边形，面积越大表示综合表现越好
            - 📊 **数值标准化**：所有指标都标准化到0-100%范围，便于比较
            - 💰 **成本类指标**：如"单次注册成本"，值越小越好，已进行反向处理
            - 📈 **效果类指标**：如"点击率"、"展示次数"，值越大越好
            - 🔍 **使用建议**：面积大且形状均匀的广告通常表现更佳
            """)

        # 添加原始数据表格
        st.subheader("📋 原始数据值")
        summary_df = pd.DataFrame()
        for ad in visible_ads:
            ad_data = filtered_df[filtered_df[ad_name_column] == ad].copy()
            row = {'广告名称': ad}
            for metric, metric_name in zip(metrics_list, metrics_names):
                avg_value = ad_data[metric].mean()
                if '率' in metric_name:
                    row[metric_name] = f"{avg_value:.2%}"
                elif '成本' in metric_name or '金额' in metric_name:
                    row[metric_name] = f"¥{avg_value:.2f}"
                else:
                    row[metric_name] = f"{avg_value:.2f}"
            summary_df = pd.concat([summary_df, pd.DataFrame([row])], ignore_index=True)

        st.dataframe(summary_df, use_container_width=True)

    except Exception as e:
        st.error(f"创建雷达图时出错: {e}")
        st.exception(e)

# 自定义CSS样式，增加字体大小并美化界面
st.markdown("""
<style>
    .big-font {
        font-size:28px !important;
    }
    .medium-font {
        font-size:22px !important;
    }
    h1 {
        font-size:36px !important;
    }
    h3 {
        font-size:24px !important;
    }
    /* 卡片式设计 */
    div.stBlock {
        background-color: #f8f9fa;
        padding: 5px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 5px;
    }
    /* 美化侧边栏 */
    .css-1d391kg {
        padding-top: 2rem;
    }
    /* 美化按钮 */
    .stButton>button {
        border-radius: 20px;
        padding: 10px 24px;
    }
    /* 美化选择框 */
    .stSelectbox>div>div {
        border-radius: 5px;
    }
    /* 美化复选框 */
    .stCheckbox {
        padding: 10px 0;
    }
    /* 美化滑块 */
    .stSlider {
        padding-top: 10px;
        padding-bottom: 10px;
    }
    /* 美化分隔线 */
    hr {
        margin-top: 30px;
        margin-bottom: 30px;
    }
    /* 美化图表容器 */
    .element-container {
        background-color: white;
        padding: 2px;
        border-radius: 10px;
        margin: 2px 0;
    }
</style>
""", unsafe_allow_html=True)

# 页面标题使用居中样式
st.markdown("<h1 style='text-align: center;'>广告数据分析平台</h1>", unsafe_allow_html=True)


# 改进侧边栏样式和组织
st.sidebar.markdown("<h2 class='medium-font' style='text-align: center;margin: -13px;'>数据控制面板</h2>", unsafe_allow_html=True)


# 添加侧边栏分组
st.sidebar.markdown('<p class="medium-font" style="text-align: center;">数据来源</p>', unsafe_allow_html=True)

# 尝试多个可能的默认文件路径
possible_paths = [
    "../../Data/deal_data/广告数据对比总表.xlsx",
    "../Data/deal_data/广告数据对比总表.xlsx",
    "./Data/deal_data/广告数据对比总表.xlsx",
    "Data/deal_data/广告数据对比总表.xlsx",
    "../../../Data/deal_data/广告数据对比总表.xlsx",
    "广告数据对比总表.xlsx"  # 当前目录
]

# 检查哪个路径存在
default_file_path = None
for path in possible_paths:
    if os.path.exists(path):
        default_file_path = path
        st.sidebar.success(f"找到默认文件: {path}")
        break

if default_file_path is None:
    st.sidebar.warning("未找到默认数据文件，请上传Excel文件进行分析。")

# 添加文件上传功能
st.sidebar.info("注意：如果上传失败，请尝试使用英文文件名，或减小文件大小")
    
# 添加文件名转换提示
st.sidebar.markdown("""
**文件名建议**:
- 使用英文字母和数字命名文件
- 避免使用中文和特殊字符
- 例如: `ad_data.xlsx` 而不是 `广告数据.xlsx`
""")

# 初始化变量
uploaded_file = None
df = None
excel_file = None
sheet_names = []
    
uploaded_file = st.sidebar.file_uploader("上传Excel文件", type=["xlsx", "xls"], accept_multiple_files=False)
    
if uploaded_file is not None:
    try:
        # 清理文件名
        safe_filename = clean_filename(uploaded_file.name)
        st.sidebar.write(f"原始文件名: {uploaded_file.name}")
        st.sidebar.write(f"安全文件名: {safe_filename}")
        
        # 直接从内存中读取文件，不保存到临时文件
        file_bytes = uploaded_file.getvalue()
        
        # 尝试直接从内存中读取Excel工作表名称
        excel_file = pd.ExcelFile(io.BytesIO(file_bytes))
        sheet_names = excel_file.sheet_names
        
        # 显示上传成功信息
        st.sidebar.success(f"成功上传文件")
    except Exception as e:
        st.sidebar.error(f"上传文件时出错: {str(e)}")
        st.sidebar.info("请尝试使用英文文件名，或减小文件大小")
        
        # 如果有默认文件，尝试使用默认文件
        if default_file_path:
            try:
                excel_file = pd.ExcelFile(default_file_path)
                sheet_names = excel_file.sheet_names
                st.sidebar.info(f"将使用默认文件: {default_file_path}")
            except Exception as e:
                st.error(f"无法读取默认文件: {str(e)}")
                st.stop()
        else:
            st.error("无法读取上传的文件，且未找到默认文件")
            st.stop()
else:
    # 如果没有上传文件，尝试使用默认文件
    if default_file_path:
        try:
            excel_file = pd.ExcelFile(default_file_path)
            sheet_names = excel_file.sheet_names
            st.sidebar.info(f"使用默认文件: {default_file_path}")
        except Exception as e:
            st.error(f"无法读取默认文件: {str(e)}")
            st.stop()
    else:
        st.error("未找到默认文件，请上传Excel文件")
        st.stop()

# 显示工作表
st.sidebar.markdown('<p class="medium-font" style="text-align: center;">工作表选择</p>', unsafe_allow_html=True)

# 尝试读取名为"台账"的工作表，如果不存在，则提供工作表选择
if "台账" in sheet_names:
    default_sheet = "台账"
else:
    default_sheet = sheet_names[0]
    
sheet_name = st.sidebar.selectbox("选择要分析的工作表:", sheet_names, index=sheet_names.index(default_sheet) if default_sheet in sheet_names else 0)

# 读取选定的工作表
try:
    if uploaded_file is not None:
        # 直接从内存中读取数据
        df = pd.read_excel(io.BytesIO(file_bytes), sheet_name=sheet_name)
        st.success(f"成功读取上传的文件, 工作表: {sheet_name}")
    elif default_file_path is not None:
        # 从默认文件中读取数据
        df = pd.read_excel(default_file_path, sheet_name=sheet_name)
        st.success(f"成功读取默认文件, 工作表: {sheet_name}")
    else:
        st.error("没有可用的数据文件")
        st.stop()
except Exception as e:
    st.error(f"读取工作表时出错: {str(e)}")
    st.info("可能是文件格式问题，请尝试另一个工作表或另一个文件")
    st.stop()


# 清理列名：去除空格，将None或NaN列名替换为有意义的名称
df.columns = [str(col).strip() if not pd.isna(col) else f"未命名列_{i}" for i, col in enumerate(df.columns)]

# 定义货币符号清理函数
def clean_currency_data(value):
    """清理包含货币符号的数据，转换为纯数值"""
    if pd.isna(value):
        return value

    # 转换为字符串进行处理
    str_value = str(value).strip()

    # 如果已经是数值，直接返回
    try:
        return float(str_value)
    except ValueError:
        pass

    # 移除常见的货币符号和格式
    # 移除USD, $, ¥, €, £等货币符号
    currency_symbols = ['USD', 'CNY', 'RMB', '$', '¥', '€', '£', '￥']
    for symbol in currency_symbols:
        str_value = str_value.replace(symbol, '')

    # 移除逗号分隔符
    str_value = str_value.replace(',', '')

    # 移除多余的空格
    str_value = str_value.strip()

    # 尝试转换为数值
    try:
        return float(str_value)
    except ValueError:
        return value  # 如果无法转换，返回原值

# 尝试自动转换数据类型，包括货币数据清理
for col in df.columns:
    # 尝试将可能的数值列转换为数值类型
    if col not in ['广告名称', '时间', '日期']:  # 排除明显的非数值列
        try:
            # 先清理货币符号，再转换为数值类型
            df[col] = df[col].apply(clean_currency_data)
            df[col] = pd.to_numeric(df[col], errors='coerce')
        except:
            pass  # 如果转换失败，保持原样

# 确定广告名称列
# 尝试找到可能的广告名称列
possible_name_columns = [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['广告名称', 'name', 'ad'])]

if possible_name_columns:
    ad_name_column = possible_name_columns[0]
else:
    # 如果没有找到明显的广告名称列，使用第一列作为默认值
    ad_name_column = df.columns[0]

# 尝试确定关键指标列
metrics_mapping = {
    '展示次数': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['展示', '次数', 'impression', 'view'])],
    '点击量': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['点击', 'click'])],
    '成效': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['成效', 'effect', 'conversion'])],
    '点击率': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['点击率', 'ctr', 'rate'])],
    '单次注册成本': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['单次注册', '成本', 'cost', 'registration'])],
    '已花费金额': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['花费', '金额', 'spend', 'cost'])]
}

# 显示找到的列映射
with st.expander("列映射"):
    st.write("系统自动识别的列映射:")
    for metric, cols in metrics_mapping.items():
        if cols:
            st.write(f"{metric}: {cols}")
        else:
            st.write(f"{metric}: 未找到匹配列")

# 让用户选择正确的列
st.subheader("选择数据列")

# 使用两列布局显示列选择
col1, col2 = st.columns(2)

# 选择广告名称列
with col1:
    # 如果有多个可能的广告名称列，提供选择
    if len(possible_name_columns) > 1:
        ad_name_column = st.selectbox(
            "选择广告名称列",
            options=possible_name_columns + [col for col in df.columns if col not in possible_name_columns],
            index=0 if len(possible_name_columns) > 3 else 0,
            help="选择包含广告名称的列"
        )
    else:
        ad_name_column = st.selectbox(
            "选择广告名称列",
            options=df.columns.tolist(),
            index=df.columns.get_loc(ad_name_column) if ad_name_column in df.columns else 0,
            help="选择包含广告名称的列"
        )

# 获取唯一的广告名称
try:
    ad_names = df[ad_name_column].dropna().unique().tolist()
except Exception as e:
    st.error(f"获取广告名称时出错: {str(e)}")
    st.info("请选择另一个广告名称列")
    st.stop()
    
if not ad_names:
    st.warning("选择的广告名称列没有有效数据，请选择另一个列")
    ad_names = ["未知广告"]

# 选择日期列（如果有）
date_columns = [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['日期', 'date', '时间', 'time'])]
date_column = None

with col2:
    if date_columns:
        # 查找包含"时间"的列
        time_columns = [col for col in date_columns if '时间' in str(col).lower()]
        default_index = 0
        
        # 如果找到了包含"时间"的列，将其设为默认选项
        if time_columns:
            default_index = date_columns.index(time_columns[0]) + 1  # +1 因为第一个选项是"无"
        
        date_column = st.selectbox(
            "选择日期列",
            options=["无"] + date_columns,
            index=default_index,
            help="选择包含日期或时间的列"
        )
    else:
        st.info("未检测到日期列")
        date_column = "无"

# 处理日期列
if date_column != "无":
    # 尝试将日期列转换为日期类型
    try:
        # 使用更安全的方式处理日期转换
        df[date_column] = pd.to_datetime(df[date_column], errors='coerce', format=None)
        
        # 检查是否有任何成功转换的日期
        if df[date_column].notna().any():
            st.success(f"成功将 {date_column} 转换为日期类型")
            
            # 添加日期范围选择
            st.subheader("选择日期范围")
            min_date = df[date_column].min().date()
            max_date = df[date_column].max().date()
            
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input("开始日期", min_date, min_value=min_date, max_value=max_date)
            with col2:
                end_date = st.date_input("结束日期", max_date, min_value=min_date, max_value=max_date)
            
            # 过滤日期范围
            df = df[(df[date_column].dt.date >= start_date) & (df[date_column].dt.date <= end_date)]
            st.info(f"已筛选日期范围: {start_date} 至 {end_date}, 共 {len(df)} 条数据")
        else:
            st.warning(f"无法将 {date_column} 转换为日期类型，将使用原始值")
            date_column = "无"
    except Exception as e:
        st.warning(f"无法将 {date_column} 转换为日期类型: {e}")
        st.warning("将使用原始值")
        date_column = "无"

# 使用卡片式设计包装主要内容区域
with st.container():
    # 使用列布局优化选择广告和指标的区域
    col1, col2 = st.columns(2)
    
    with col1:

        st.markdown('<p class="big-font">选择要分析的广告</p>', unsafe_allow_html=True)

        # 初始化广告选择状态
        if 'ads_multiselect' not in st.session_state:
            st.session_state.ads_multiselect = []

        # 定义callback函数
        def select_all_ads():
            st.session_state.ads_multiselect = ad_names

        def clear_all_ads():
            st.session_state.ads_multiselect = []

        # 添加全选/全不选按钮
        col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 2])

        with col_btn1:
            st.button("🔘 全选", help="选择所有广告", on_click=select_all_ads, key="select_all_ads")

        with col_btn2:
            st.button("⭕ 全不选", help="清空所有选择", on_click=clear_all_ads, key="clear_all_ads")

        # 使用多选框选择广告
        selected_ads = st.multiselect(
            "选择广告名称",
            options=ad_names,
            help="可以选择多个广告进行对比分析",
            key="ads_multiselect"
        )
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:

        st.markdown('<p class="big-font">选择要分析的指标</p>', unsafe_allow_html=True)
        
        # 默认选择全部指标
        default_metrics = list(metrics_mapping.keys())
        
        # 添加"全选"选项，默认为True
        select_all_metrics = st.checkbox("全选指标", value=True)
        
        if select_all_metrics:
            selected_metrics = list(metrics_mapping.keys())
        else:
            # 使用多选框选择指标
            selected_metrics = st.multiselect(
                "选择要分析的指标",
                options=list(metrics_mapping.keys()),
                default=default_metrics,
                help="可以选择多个指标进行分析"
            )
        st.markdown('</div>', unsafe_allow_html=True)

# 为选择的指标选择对应的列
metrics_columns = {}

# 只为选定的指标选择列
if selected_metrics:

    st.markdown('<p class="big-font">选择指标列</p>', unsafe_allow_html=True)
    
    # 使用更多列布局显示指标选择
    cols = st.columns(min(4, len(selected_metrics)))
    
    for i, metric in enumerate(selected_metrics):
        with cols[i % len(cols)]:
            default_index = 0
            if metrics_mapping[metric]:
                default_col = metrics_mapping[metric][0]
                if default_col in df.columns:
                    default_index = df.columns.get_loc(default_col)
            
            metrics_columns[metric] = st.selectbox(
                f"选择{metric}列",
                options=df.columns.tolist(),
                index=default_index,
                help=f"选择包含{metric}数据的列"
            )
    st.markdown('</div>', unsafe_allow_html=True)

# 初始化图表可见性控制的session state
if 'chart_visibility' not in st.session_state:
    st.session_state.chart_visibility = {}

# 确保所有选中的广告都在chart_visibility中，并且默认为可见
if selected_ads:
    for ad in selected_ads:
        if ad not in st.session_state.chart_visibility:
            st.session_state.chart_visibility[ad] = True

# 检查数据是否有效
if selected_ads and selected_metrics:
    # 检查选定的广告是否存在于数据中
    filtered_df = df[df[ad_name_column].isin(selected_ads)]
    if filtered_df.empty:
        st.error(f"所选广告 {selected_ads} 在数据中不存在。请检查广告名称列是否正确。")
        st.stop()
    
    # 检查指标列是否包含有效数据
    for metric_name, metric_column in metrics_columns.items():
        if metric_column not in df.columns:
            st.error(f"指标列 {metric_column} 不存在于数据中。")
            st.stop()
        
        # 检查该列是否包含数值数据
        if not pd.api.types.is_numeric_dtype(filtered_df[metric_column]):
            try:
                # 尝试转换为数值类型
                filtered_df[metric_column] = pd.to_numeric(filtered_df[metric_column], errors='coerce')
                # 更新原始数据框
                df.loc[filtered_df.index, metric_column] = filtered_df[metric_column]
                st.warning(f"已将 {metric_column} 列转换为数值类型，可能有部分数据被转换为NaN")
            except:
                st.error(f"指标列 {metric_column} 不包含数值数据，无法生成图表。")
                st.stop()
    
    # 添加图表显示选项

    st.markdown('<p class="big-font">图表显示选项</p>', unsafe_allow_html=True)
    
    # 使用列布局优化图表选项
    col1, col2, col3 = st.columns(3)
    with col1:
        chart_height = st.slider("图表高度", min_value=400, max_value=800, value=450, step=50)
    with col2:
        show_grid = st.checkbox("显示网格线", value=True)
    with col3:
        theme_options = ["默认", "浅色", "深色"]
        chart_theme = st.selectbox("图表主题", theme_options, index=2)

    # 将目标线设置移到更显眼的位置
    st.markdown('<p class="big-font">目标线设置</p>', unsafe_allow_html=True)  # 使用更大的标题
    target_values = {}

    # 使用两列布局显示目标线设置
    col1, col2 = st.columns(2)
    for i, metric_name in enumerate(selected_metrics):
        # 设置默认目标值
        default_value = 0.0
        if metric_name == '点击率':
            default_value = 0.1
        elif metric_name == '单次注册成本':
            default_value = 0.5
        
        # 交替在两列中显示目标线设置
        with col1 if i % 2 == 0 else col2:
            target_values[metric_name] = st.number_input(
                f"{metric_name}目标值", 
                value=default_value,
                key=f"target_{metric_name}",
                step=0.01,
                format="%.2f",
                help=f"设置{metric_name}的目标值，非零值将在图表中显示目标线"
            )

    st.markdown('</div>', unsafe_allow_html=True)

    # 添加图表联动控制面板
    if selected_ads:
        st.markdown('<p class="big-font">📊 图表联动控制</p>', unsafe_allow_html=True)
        st.markdown("控制每个广告在所有图表中的显示状态：")

        # 初始化所有广告的可见性状态
        for ad in selected_ads:
            if ad not in st.session_state.chart_visibility:
                st.session_state.chart_visibility[ad] = True

        # 定义图表联动控制的callback函数
        def show_all_charts():
            for ad in selected_ads:
                st.session_state.chart_visibility[ad] = True

        def hide_all_charts():
            for ad in selected_ads:
                st.session_state.chart_visibility[ad] = False

        # 添加全选/全不选按钮
        col_chart_btn1, col_chart_btn2, col_chart_btn3 = st.columns([1, 1, 2])

        with col_chart_btn1:
            st.button("👁️ 全部显示", help="显示所有广告图表", on_click=show_all_charts, key="show_all_charts")

        with col_chart_btn2:
            st.button("🙈 全部隐藏", help="隐藏所有广告图表", on_click=hide_all_charts, key="hide_all_charts")

        # 创建广告可见性控制
        cols = st.columns(min(4, len(selected_ads)))
        for i, ad in enumerate(selected_ads):
            with cols[i % 4]:
                # 使用checkbox控制广告可见性，直接使用session state管理
                visibility = st.checkbox(
                    f"显示 {ad}",
                    value=st.session_state.chart_visibility.get(ad, True),
                    key=f"visibility_{ad}",
                    help=f"控制 {ad} 在所有图表中的显示状态"
                )
                # 更新session state
                st.session_state.chart_visibility[ad] = visibility

        st.markdown("---")  # 添加分隔线

    # 设置图表主题
    chart_bg_color = 'white'
    chart_grid_color = 'LightGray'
    chart_text_color = 'black'
    
    if chart_theme == "深色":
        chart_bg_color = '#1f2630'
        chart_grid_color = '#3b4754'
        chart_text_color = 'white'
    
    # 绘制折线图
    if all(metric in df.columns for metric in metrics_columns.values()):
        # 获取备注字典，传入上传的文件和默认文件路径
        remarks_dict = get_remarks_dict(uploaded_file=uploaded_file, excel_file_path=default_file_path)

        st.markdown('<p class="big-font" style="text-align: center;"><b>📈 广告数据分析</b></p>', unsafe_allow_html=True)

        # 创建指标分析标签页 - 为不同指标添加专门的图标
        metric_names = list(metrics_columns.keys())

        # 为不同指标定义专门的图标
        metric_icons = {
            '成效': '🎯',
            '点击率': '👆',
            '点击量': '🖱️',
            '展示次数': '👁️',
            '已花费金额': '💰',
            '单次注册成本': '💵',
            '转化率': '📈',
            '注册量': '📝',
            '成本': '💸'
        }

        # 创建带图标的标签页标题
        tab_titles = []
        for name in metric_names:
            # 查找匹配的图标
            icon = '📊'  # 默认图标
            for key, emoji in metric_icons.items():
                if key in name:
                    icon = emoji
                    break
            tab_titles.append(f"{icon} {name}")

        # 如果选择了多个指标，添加综合分析标签页
        if len(selected_metrics) > 1:
            tab_titles.append("🔍 综合分析")

        metric_tabs = st.tabs(tab_titles)

        # 为每个指标创建专门的标签页
        for tab_idx, (metric_name, metric_column) in enumerate(metrics_columns.items()):
            with metric_tabs[tab_idx]:
                # 添加标签页标题和描述
                st.markdown(f'<p class="medium-font" style="text-align: center; margin-bottom: 10px;"><b>{metric_name}分析</b></p>', unsafe_allow_html=True)

                # 根据指标类型添加简短描述
                if '成效' in metric_name:
                    st.info("📊 分析广告成效表现，了解整体投放效果")
                elif '点击率' in metric_name:
                    st.info("👆 分析点击率趋势，优化广告吸引力")
                elif '点击量' in metric_name:
                    st.info("🖱️ 分析点击量变化，了解用户互动情况")
                elif '展示次数' in metric_name:
                    st.info("👁️ 分析展示次数趋势，了解广告曝光情况")
                elif '金额' in metric_name or '成本' in metric_name:
                    st.info("💰 分析成本投入情况，优化预算分配")
                else:
                    st.info(f"📈 分析{metric_name}的变化趋势和表现")

                # 获取当前指标的目标值
                target_value = target_values.get(metric_name, 0.0)

                # 获取当前可见的广告列表
                visible_ads = [ad for ad in selected_ads if st.session_state.chart_visibility.get(ad, True)]

                # 过滤数据 - 只显示可见的广告
                filtered_df = df[df[ad_name_column].isin(visible_ads)].copy()

                # 确保数据是数值类型
                filtered_df[metric_column] = pd.to_numeric(filtered_df[metric_column], errors='coerce')

                # 检查是否有足够的数据点
                if filtered_df[metric_column].count() < 1:
                    st.warning(f"没有足够的有效数据点来绘制 {metric_name} 的图表")
                    continue

                # 使用Plotly创建交互式折线图
                fig = go.Figure()

                # 如果有日期列，按日期排序
                if date_column != "无":
                        filtered_df = filtered_df.sort_values(by=date_column)

                        # 为每个可见广告绘制折线
                        for ad in visible_ads:
                            ad_data = filtered_df[filtered_df[ad_name_column] == ad].copy()
                            if not ad_data.empty and ad_data[metric_column].count() > 0:
                                # 准备悬停文本，包含备注信息
                                hover_texts = []
                                for i, row in ad_data.iterrows():
                                    # 获取日期字符串
                                    date_str = ""
                                    if date_column != "无":
                                        date_val = row[date_column]
                                        if isinstance(date_val, (datetime, pd.Timestamp)):
                                            date_str = date_val.strftime('%Y/%m/%d')
                                        else:
                                            date_str = str(date_val)

                                    hover_text = f"{ad}<br>{date_str}<br>{metric_name}: "
                                    if '金额' in metric_name or '成本' in metric_name:
                                        hover_text += f"¥{row[metric_column]:.2f}"
                                    elif '率' in metric_name:
                                        hover_text += f"{row[metric_column]:.2%}"
                                    else:
                                        hover_text += f"{row[metric_column]:.2f}"

                                    # 添加备注信息（如果有）
                                    ad_name = row.get(ad_name_column, "")
                                    date_str_for_key = ""
                                    if date_column != "无":
                                        date_val = row[date_column]
                                        if isinstance(date_val, (datetime, pd.Timestamp)):
                                            date_str_for_key = date_val.strftime('%Y/%m/%d')
                                        else:
                                            date_str_for_key = str(date_val)
                                    key = f"{ad_name}_{date_str_for_key}"

                                    if key in remarks_dict:
                                        hover_text += f"<br>备注: {remarks_dict[key]}"

                                    hover_texts.append(hover_text)

                                # 安全处理日期格式
                                fig.add_trace(go.Scatter(
                                    x=ad_data[date_column],
                                    y=ad_data[metric_column],
                                    mode='lines+markers+text',
                                    name=ad,
                                    text=[f"¥{val:.2f}" if ('金额' in metric_name or '成本' in metric_name) else
                                          f"{val:.2%}" if '率' in metric_name else
                                          f"{val:.2f}" for val in ad_data[metric_column]],
                                    textposition='top center',
                                    textfont=dict(size=14, color=chart_text_color),
                                    hovertemplate="%{hovertext}<extra></extra>",
                                    hovertext=hover_texts,
                                    hoverlabel=dict(bgcolor="white", font_size=14, font_family="Arial")
                                ))

                        fig.update_xaxes(title_text='日期')
                else:
                    # 如果没有日期列，使用索引作为x轴
                    for ad in visible_ads:
                        ad_data = filtered_df[filtered_df[ad_name_column] == ad].copy()
                        if not ad_data.empty and ad_data[metric_column].count() > 0:
                            # 准备悬停文本，包含备注信息
                            hover_texts = []
                            for i, row in ad_data.iterrows():
                                hover_text = f"{ad}<br>数据点: {i}<br>{metric_name}: "
                                if '金额' in metric_name or '成本' in metric_name:
                                    hover_text += f"¥{row[metric_column]:.2f}"
                                elif '率' in metric_name:
                                    hover_text += f"{row[metric_column]:.2%}"
                                else:
                                    hover_text += f"{row[metric_column]:.2f}"

                                # 添加备注信息（如果有）
                                ad_name = row.get(ad_name_column, "")
                                date_str_for_key = ""
                                if date_column != "无":
                                    date_val = row[date_column]
                                    if isinstance(date_val, (datetime, pd.Timestamp)):
                                        date_str_for_key = date_val.strftime('%Y/%m/%d')
                                    else:
                                        date_str_for_key = str(date_val)
                                key = f"{ad_name}_{date_str_for_key}"

                                if key in remarks_dict:
                                    hover_text += f"<br>备注: {remarks_dict[key]}"

                                hover_texts.append(hover_text)

                            # 安全处理日期格式
                            fig.add_trace(go.Scatter(
                                x=list(range(len(ad_data))),
                                y=ad_data[metric_column],
                                mode='lines+markers+text',  # 添加text模式以显示数值
                                name=ad,
                                text=[f"¥{val:.2f}" if ('金额' in metric_name or '成本' in metric_name) else
                                      f"{val:.2%}" if '率' in metric_name else
                                      f"{val:.2f}" for val in ad_data[metric_column]],  # 根据指标类型格式化
                                textposition='top center',  # 将文本放在点的上方
                                textfont=dict(size=14, color=chart_text_color),  # 增加文本字体大小
                                hovertemplate=f"{ad}<br>数据点: %{{x}}<br>{metric_name}: %{{y}}<extra></extra>",
                                hovertext=hover_texts,
                                hoverlabel=dict(bgcolor="white", font_size=14, font_family="Arial")
                            ))

                    fig.update_xaxes(title_text='数据点')

                # 设置图表布局
                fig.update_layout(
                    title=dict(
                        text=f'不同广告的{metric_name}走势对比',
                        font=dict(size=20, color=chart_text_color)  # 增加标题字体大小
                    ),
                    yaxis_title=dict(
                        text=metric_name,
                        font=dict(size=16, color=chart_text_color)  # 增加y轴标题字体大小
                    ),
                    xaxis_title=dict(
                        text='日期' if date_column != "无" else '数据点',
                        font=dict(size=16, color=chart_text_color)  # 增加x轴标题字体大小
                    ),
                    hovermode="closest",
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1,
                        font=dict(size=14, color=chart_text_color)  # 增加图例字体大小
                    ),
                    height=chart_height,
                    width=800,
                    plot_bgcolor=chart_bg_color,
                    paper_bgcolor=chart_bg_color,
                    font=dict(color=chart_text_color)
                )

                # 添加显示数值选项
                show_values = st.checkbox(f"在{metric_name}图表上显示数值", value=True)

                # 根据用户选择显示或隐藏数值
                for trace in fig.data:
                    if show_values:
                        trace.textposition = 'top center'
                    else:
                        trace.textposition = 'none'

                # 设置y轴格式（对于金额和百分比）
                if '金额' in metric_name or '成本' in metric_name:
                    fig.update_yaxes(tickprefix="¥", tickformat=",.2f")
                elif '率' in metric_name:
                    fig.update_yaxes(tickformat=".2%")

                # 添加网格线
                if show_grid:
                    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor=chart_grid_color)
                    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor=chart_grid_color)
                else:
                    fig.update_xaxes(showgrid=False)
                    fig.update_yaxes(showgrid=False)

                # 在创建图表后添加目标线
                if target_value != 0.0:
                    fig.add_hline(
                        y=target_value,
                        line_dash="dash",
                        line_color="red",
                        annotation_text=f"目标线: {target_value:.2f}",
                        annotation_position="bottom right",
                        annotation_font_size=14,
                        annotation_font_color="red"
                    )

                # 显示交互式图表
                st.plotly_chart(fig, use_container_width=True)

                # 显示数据表格，包括备注
                with st.expander(f"{metric_name}数据表"):
                    # 准备要显示的数据
                    display_df = filtered_df.copy()

                    # 添加备注列 - 使用组合键查找备注
                    display_df['备注'] = ""
                    for idx, row in display_df.iterrows():
                        ad_name = row[ad_name_column]
                        date_str_for_key = ""
                        if date_column != "无":
                            date_val = row[date_column]
                            if isinstance(date_val, (datetime, pd.Timestamp)):
                                date_str_for_key = date_val.strftime('%Y/%m/%d')
                            else:
                                date_str_for_key = str(date_val)

                        # 首先尝试使用广告名称+日期作为键
                        key = f"{ad_name}_{date_str_for_key}"
                        if key in remarks_dict:
                            display_df.at[idx, '备注'] = str(remarks_dict[key])
                        # 如果没有找到，尝试仅使用广告名称
                        elif ad_name in remarks_dict:
                            display_df.at[idx, '备注'] = str(remarks_dict[ad_name])

                    # 明确设置备注列的数据类型为字符串
                    display_df['备注'] = display_df['备注'].astype(str)

                    # 准备要显示的列
                    show_columns = [ad_name_column]
                    if date_column != "无":
                        show_columns.append(date_column)
                    show_columns.append(metric_column)
                    show_columns.append('备注')  # 添加备注列

                    # 显示数据表
                    st.dataframe(display_df[show_columns], use_container_width=True)

                    # 添加下载功能
                    if st.button(f"下载{metric_name}数据表"):
                        # 创建临时Excel文件
                        output = io.BytesIO()
                        with pd.ExcelWriter(output, engine='openpyxl') as writer:
                            display_df[show_columns].to_excel(writer, index=False, sheet_name=f"{metric_name}数据")

                        # 提供下载链接
                        output.seek(0)
                        st.download_button(
                            label=f"下载{metric_name}Excel表格",
                            data=output,
                            file_name=f"{metric_name}_数据表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        )
                st.markdown('</div>', unsafe_allow_html=True)

        # 如果选择了多个指标，添加综合分析标签页
        if len(selected_metrics) > 1:
            with metric_tabs[-1]:  # 最后一个标签页是综合分析
                st.markdown('<p class="medium-font" style="text-align: center; margin-bottom: 10px;"><b>综合分析</b></p>', unsafe_allow_html=True)
                st.info("🔍 多维度对比分析，通过雷达图展示各广告在不同指标上的综合表现")

                # 为每个广告创建雷达图
                if len(selected_ads) > 0:
                    # 使用新的雷达图函数
                    create_radar_chart(
                        filtered_df=df[df[ad_name_column].isin(selected_ads)].copy(),
                        selected_ads=selected_ads,
                        ad_name_column=ad_name_column,
                        metrics_columns=metrics_columns,
                        chart_height=chart_height,
                        chart_bg_color=chart_bg_color,
                        chart_text_color=chart_text_color
                    )
                else:
                    st.warning("请先选择要分析的广告")
                st.markdown('</div>', unsafe_allow_html=True)

# 添加备注数据展示部分（放在图表和雷达图之后）
if not df.empty:
    # 使用水平分隔线分隔不同部分
    st.markdown("<hr>", unsafe_allow_html=True)
    display_remarks(df, uploaded_file=uploaded_file, default_file_path=default_file_path)

# 添加页脚

st.markdown("<p style='text-align: center; color: gray;'>© 2025 广告数据分析平台</p>", unsafe_allow_html=True)