#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
找到真正的最后一行有数据的位置
"""

from openpyxl import load_workbook

# 加载工作簿
wb = load_workbook('juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx')
ws = wb['台账']

print("=== 寻找真正的最后一行有数据的位置 ===")

# 方法1：从第1行开始逐行检查
print("方法1：逐行检查（前1000行）")
last_data_row_method1 = 1
for row in range(1, 1001):
    row_has_data = False
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row, col).value
        if cell_value is not None and str(cell_value).strip() != '':
            row_has_data = True
            break
    
    if row_has_data:
        last_data_row_method1 = row

print(f"方法1结果：最后一行有数据是第{last_data_row_method1}行")

# 方法2：检查关键列（广告账户、广告名称等）
print("\n方法2：检查关键列（A、E列）")
last_data_row_method2 = 1
for row in range(1, 1001):
    # 检查A列（广告账户）和E列（广告名称）
    a_value = ws.cell(row, 1).value  # A列
    e_value = ws.cell(row, 5).value  # E列
    
    if (a_value is not None and str(a_value).strip() != '') or \
       (e_value is not None and str(e_value).strip() != ''):
        last_data_row_method2 = row

print(f"方法2结果：最后一行有数据是第{last_data_row_method2}行")

# 显示最后几行的关键数据
print(f"\n显示第{last_data_row_method2-5}行到第{last_data_row_method2+5}行的关键数据：")
for row in range(max(1, last_data_row_method2-5), last_data_row_method2+6):
    a_value = ws.cell(row, 1).value  # 广告账户
    b_value = ws.cell(row, 2).value  # 时间
    e_value = ws.cell(row, 5).value  # 广告名称
    g_value = ws.cell(row, 7).value  # 展示次数
    
    print(f"第{row}行: A={a_value}, B={b_value}, E={e_value}, G={g_value}")

print(f"\n=== 真正的最后一行应该是第{last_data_row_method2}行 ===")
