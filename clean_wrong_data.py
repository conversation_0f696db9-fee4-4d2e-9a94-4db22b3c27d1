#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理错误写入的数据
"""

from openpyxl import load_workbook
import shutil
from datetime import datetime

# 创建备份
TARGET_FILE_PATH = r"juadge_data_deal\Data\deal_data\广告数据对比总表.xlsx"
backup_path = TARGET_FILE_PATH.replace('.xlsx', f'_backup_before_clean_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
shutil.copy2(TARGET_FILE_PATH, backup_path)
print(f"已创建备份文件: {backup_path}")

# 加载工作簿
wb = load_workbook(TARGET_FILE_PATH)
ws = wb['台账']

print("=== 清理错误写入的数据 ===")

# 清理第501-512行的数据
print("清理第501-512行的数据...")
for row in range(501, 513):
    for col in range(1, ws.max_column + 1):
        ws.cell(row, col).value = None

# 保存文件
wb.save(TARGET_FILE_PATH)
print("清理完成！")

# 验证清理结果
print("\n验证清理结果（第501-512行）：")
for row in range(501, 513):
    row_data = []
    for col in range(1, 8):
        row_data.append(ws.cell(row, col).value)
    
    if any(val is not None for val in row_data):
        print(f"第{row}行仍有数据: {row_data}")
    else:
        print(f"第{row}行已清空")

print("\n=== 清理完成 ===")
