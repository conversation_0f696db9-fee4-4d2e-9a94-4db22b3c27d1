#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查目标工作表的情况
"""

from openpyxl import load_workbook

# 加载工作簿
wb = load_workbook('juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx')
ws = wb['台账']

print("=== 目标工作表分析 ===")
print(f"工作表最大行数: {ws.max_row}")
print(f"工作表最大列数: {ws.max_column}")

# 显示标题行
print("\n标题行（前15列）:")
header_row = []
for i in range(1, 16):
    header_row.append(ws.cell(1, i).value)
print(header_row)

# 寻找最后一行有数据的位置
print("\n寻找最后一行有数据的位置:")
last_data_row = 1
for row in range(1, 600):  # 检查前600行
    if any(ws.cell(row, col).value is not None for col in range(1, ws.max_column + 1)):
        last_data_row = row

print(f"最后一行有数据: 第{last_data_row}行")

# 显示最后几行的数据
print(f"\n最后几行的数据（第{max(1, last_data_row-2)}行到第{last_data_row}行）:")
for i in range(max(1, last_data_row-2), last_data_row+1):
    row_data = []
    for j in range(1, 8):  # 显示前7列
        row_data.append(ws.cell(i, j).value)
    print(f"第{i}行: {row_data}")

# 检查新写入的数据（第501-512行）
print(f"\n检查新写入的数据（第501-512行）:")
for i in range(501, 513):
    row_data = []
    for j in range(1, 8):  # 显示前7列
        row_data.append(ws.cell(i, j).value)
    if any(val is not None for val in row_data):
        print(f"第{i}行: {row_data}")

print("\n=== 分析完成 ===")
