#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广告数据合并处理脚本
功能：将原始广告数据文件合并到主数据表中
作者：AI Assistant
创建时间：2025-01-01
"""

import os
import glob
import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
import re
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ad_data_merger.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ==================== 配置区域 ====================
# 路径配置
ORIGIN_DATA_DIR = r"c:\Users\<USER>\Desktop\AdDataAnalyzer\juadge_data_deal\Data\origin_data"
TARGET_FILE_PATH = r"c:\Users\<USER>\Desktop\AdDataAnalyzer\juadge_data_deal\Data\deal_data\广告数据对比总表.xlsx"
TARGET_SHEET_NAME = "台账"

# 需要读取的列名（原始文件中的列名）
REQUIRED_COLUMNS = [
    '广告名称',
    '帐户名称',
    '展示次数',
    '点击量（全部）',
    '网站完成注册次数',
    '已花费金额 (USD)',
    '报告结束日期'
]

# 列名映射关系（原始列名 -> 目标列名）
COLUMN_MAPPING = {
    '广告名称': '广告名称',
    '帐户名称': '广告账户',
    '展示次数': '展示次数',
    '点击量（全部）': '点击量（全部）',
    '网站完成注册次数': '成效',
    '已花费金额 (USD)': '已花费金额 (USD)',
    '报告结束日期': '时间'
}

# 目标工作表的列顺序（按照实际工作表的列顺序）
TARGET_COLUMN_ORDER = [
    '广告账户',      # 列A
    '时间',          # 列B
    '广告系列',      # 列C (需要公式)
    '广告组',        # 列D (需要公式)
    '广告名称',      # 列E
    '短剧编号',      # 列F (需要公式)
    '展示次数',      # 列G
    '千次展示费用 (USD)', # 列H (需要公式)
    '点击量（全部）', # 列I
    '调整决策',      # 列J (空)
    '成效',          # 列K
    '点击注册转化率', # 列L (需要公式)
    '展示注册转化率', # 列M (需要公式)
    '点击率',        # 列N (需要公式)
    '单次注册成本',  # 列O (需要公式)
    '已花费金额 (USD)' # 列P
]

# 需要填充公式的列名
FORMULA_COLUMNS = [
    '广告系列',
    '广告组',
    '短剧编号',
    '千次展示费用 (USD)',
    '点击注册转化率',
    '展示注册转化率',
    '点击率',
    '单次注册成本'
]

# ==================== 工具函数 ====================
def convert_account_name(account_name):
    """
    转换账户名称格式：Mioeye--06 -> 6
    """
    if pd.isna(account_name):
        return account_name
    
    account_str = str(account_name)
    if '--' in account_str:
        # 提取--后面的部分
        parts = account_str.split('--')
        if len(parts) > 1:
            # 去除前导零，但保留单独的0
            number_part = parts[-1].lstrip('0') or '0'
            return number_part
    
    return account_str

def convert_date_format(date_value):
    """
    转换日期格式：2025-07-31 -> 2025/7/31
    """
    if pd.isna(date_value):
        return date_value
    
    try:
        # 如果已经是datetime对象
        if isinstance(date_value, pd.Timestamp):
            date_obj = date_value
        else:
            # 尝试解析字符串
            date_obj = pd.to_datetime(date_value)
        
        # 格式化为目标格式，去除月份和日期的前导零
        formatted_date = date_obj.strftime('%Y/%m/%d')
        # 去除月份和日期的前导零
        formatted_date = re.sub(r'/0(\d)', r'/\1', formatted_date)
        return formatted_date
    except Exception as e:
        logger.warning(f"日期转换失败: {date_value}, 错误: {e}")
        return str(date_value)

def validate_file_columns(df, filename):
    """
    验证文件是否包含所需的列
    """
    missing_columns = []
    for col in REQUIRED_COLUMNS:
        if col not in df.columns:
            missing_columns.append(col)
    
    if missing_columns:
        logger.warning(f"文件 {filename} 缺少以下列: {missing_columns}")
        return False
    return True

# ==================== 主要处理函数 ====================
def read_origin_files():
    """
    读取原始数据目录中的所有xlsx文件
    """
    logger.info("开始读取原始数据文件...")
    
    # 获取所有xlsx文件
    xlsx_files = glob.glob(os.path.join(ORIGIN_DATA_DIR, "*.xlsx"))
    
    if not xlsx_files:
        logger.error(f"在目录 {ORIGIN_DATA_DIR} 中未找到xlsx文件")
        return pd.DataFrame()
    
    logger.info(f"找到 {len(xlsx_files)} 个xlsx文件")
    
    all_data = []
    processed_files = 0
    
    for file_path in xlsx_files:
        filename = os.path.basename(file_path)
        logger.info(f"正在处理文件: {filename}")
        
        try:
            # 先读取文件获取列名
            df_header = pd.read_excel(file_path, nrows=0)  # 只读取列名

            # 验证文件列
            if not validate_file_columns(df_header, filename):
                logger.warning(f"跳过文件 {filename}（缺少必要列）")
                continue

            # 读取Excel文件，跳过第一行（汇总行），但保持列名
            df = pd.read_excel(file_path, skiprows=[1])  # 跳过第二行（索引1），保留列名
            
            # 只保留需要的列
            df = df[REQUIRED_COLUMNS].copy()
            
            # 删除所有列都为空的行
            df = df.dropna(how='all')
            
            if len(df) > 0:
                all_data.append(df)
                processed_files += 1
                logger.info(f"成功读取文件 {filename}，数据行数: {len(df)}")
            else:
                logger.warning(f"文件 {filename} 没有有效数据")
                
        except Exception as e:
            logger.error(f"读取文件 {filename} 时出错: {e}")
            continue
    
    if not all_data:
        logger.error("没有成功读取任何数据文件")
        return pd.DataFrame()
    
    # 合并所有数据
    combined_df = pd.concat(all_data, ignore_index=True)
    logger.info(f"成功处理 {processed_files} 个文件，总数据行数: {len(combined_df)}")
    
    return combined_df

def process_data(df):
    """
    处理数据：转换格式、重命名列、排序
    """
    if df.empty:
        return df
    
    logger.info("开始处理数据...")
    
    # 创建数据副本
    processed_df = df.copy()
    
    # 转换账户名称格式
    logger.info("转换账户名称格式...")
    processed_df['帐户名称'] = processed_df['帐户名称'].apply(convert_account_name)
    
    # 转换日期格式
    logger.info("转换日期格式...")
    processed_df['报告结束日期'] = processed_df['报告结束日期'].apply(convert_date_format)
    
    # 重命名列
    logger.info("重命名列...")
    processed_df = processed_df.rename(columns=COLUMN_MAPPING)
    
    # 按广告账户排序（数字排序）
    logger.info("按账户名称排序...")
    try:
        # 尝试将账户名称转换为数字进行排序
        processed_df['_sort_key'] = pd.to_numeric(processed_df['广告账户'], errors='coerce')
        processed_df = processed_df.sort_values('_sort_key', na_position='last')
        processed_df = processed_df.drop('_sort_key', axis=1)
    except Exception as e:
        logger.warning(f"数字排序失败，使用字符串排序: {e}")
        processed_df = processed_df.sort_values('广告账户')
    
    logger.info(f"数据处理完成，最终数据行数: {len(processed_df)}")
    return processed_df

def write_to_target_file(df):
    """
    将处理后的数据写入目标Excel文件
    """
    if df.empty:
        logger.error("没有数据需要写入")
        return False
    
    logger.info("开始写入目标文件...")
    
    try:
        # 检查目标文件是否存在
        if not os.path.exists(TARGET_FILE_PATH):
            logger.error(f"目标文件不存在: {TARGET_FILE_PATH}")
            return False
        
        # 创建备份
        backup_path = TARGET_FILE_PATH.replace('.xlsx', f'_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
        import shutil
        shutil.copy2(TARGET_FILE_PATH, backup_path)
        logger.info(f"已创建备份文件: {backup_path}")
        
        # 加载工作簿
        wb = load_workbook(TARGET_FILE_PATH)
        
        # 检查工作表是否存在
        if TARGET_SHEET_NAME not in wb.sheetnames:
            logger.error(f"目标工作表不存在: {TARGET_SHEET_NAME}")
            return False
        
        ws = wb[TARGET_SHEET_NAME]
        
        # 找到现有数据的最后一行
        logger.info("正在寻找最后一行有数据的位置...")
        actual_last_row = 1

        # 从后往前检查，找到第一个有数据的行（更高效）
        for row in range(2000, 0, -1):  # 从第2000行往前检查
            # 检查关键列：A列（广告账户）和E列（广告名称）
            a_value = ws.cell(row, 1).value  # A列
            e_value = ws.cell(row, 5).value  # E列

            if (a_value is not None and str(a_value).strip() != '') or \
               (e_value is not None and str(e_value).strip() != ''):
                actual_last_row = row
                break

        start_row = actual_last_row + 1
        logger.info(f"实际数据最后一行: {actual_last_row}，从第{start_row}行开始写入数据")
        rows_added = 0

        # 按照目标工作表的列顺序写入数据
        for idx, (_, row) in enumerate(df.iterrows()):
            current_row = start_row + idx

            # 按照TARGET_COLUMN_ORDER的顺序写入数据
            for col_idx, col_name in enumerate(TARGET_COLUMN_ORDER, 1):
                if col_name in row.index:
                    # 如果数据中有这一列，直接写入
                    ws.cell(row=current_row, column=col_idx, value=row[col_name])
                elif col_name == '调整决策':
                    # 调整决策列留空
                    ws.cell(row=current_row, column=col_idx, value=None)
                # 其他需要公式的列暂时留空，后续填充公式

            rows_added += 1
        
        logger.info(f"已添加 {rows_added} 行数据，起始行: {start_row}")
        
        # 保存文件
        wb.save(TARGET_FILE_PATH)
        logger.info("数据写入完成")
        
        return True
        
    except Exception as e:
        logger.error(f"写入目标文件时出错: {e}")
        return False

def add_formulas(start_row, end_row):
    """
    为指定行范围添加计算公式
    注意：这里提供公式模板，用户可以根据实际需求修改
    """
    logger.info(f"开始为行 {start_row} 到 {end_row} 添加公式...")
    
    try:
        wb = load_workbook(TARGET_FILE_PATH)
        ws = wb[TARGET_SHEET_NAME]
        
        # 公式模板（根据实际列位置调整）
        # 列位置：A=广告账户, B=时间, C=广告系列, D=广告组, E=广告名称, F=短剧编号, G=展示次数, H=千次展示费用, I=点击量, J=调整决策, K=成效, L=点击注册转化率, M=展示注册转化率, N=点击率, O=单次注册成本, P=已花费金额

        formula_templates = {
            'C': '=VLOOKUP(E{row},Sheet2!C:E,2,0)',      # 广告系列 - 根据现有模式
            'D': '=VLOOKUP(E{row},Sheet2!C:E,3,0)',      # 广告组 - 根据现有模式
            'F': '=VLOOKUP(E{row},[1]唯一广告名称!A:B,2,0)', # 短剧编号 - 根据现有模式
            'H': '=IF(G{row}>0,P{row}/G{row}*1000,"")',  # 千次展示费用 = 已花费金额/展示次数*1000
            'L': '=IF(I{row}>0,K{row}/I{row}*100,"")',   # 点击注册转化率 = 成效/点击量*100%
            'M': '=IF(G{row}>0,K{row}/G{row}*100,"")',   # 展示注册转化率 = 成效/展示次数*100%
            'N': '=IF(G{row}>0,I{row}/G{row}*100,"")',   # 点击率 = 点击量/展示次数*100%
            'O': '=IF(K{row}>0,P{row}/K{row},"")'        # 单次注册成本 = 已花费金额/成效
        }
        
        # 为每一行添加公式
        for row_num in range(start_row, end_row + 1):
            for col, formula_template in formula_templates.items():
                if formula_template:  # 只有非空的公式模板才添加
                    formula = formula_template.format(row=row_num)
                    ws[f'{col}{row_num}'] = formula
        
        wb.save(TARGET_FILE_PATH)
        logger.info("公式添加完成")
        return True
        
    except Exception as e:
        logger.error(f"添加公式时出错: {e}")
        return False

def main():
    """
    主函数
    """
    logger.info("=" * 50)
    logger.info("广告数据合并处理开始")
    logger.info("=" * 50)
    
    try:
        # 检查路径
        if not os.path.exists(ORIGIN_DATA_DIR):
            logger.error(f"原始数据目录不存在: {ORIGIN_DATA_DIR}")
            return
        
        if not os.path.exists(TARGET_FILE_PATH):
            logger.error(f"目标文件不存在: {TARGET_FILE_PATH}")
            return
        
        # 1. 读取原始数据文件
        raw_data = read_origin_files()
        if raw_data.empty:
            logger.error("没有读取到任何数据，程序结束")
            return
        
        # 2. 处理数据
        processed_data = process_data(raw_data)
        if processed_data.empty:
            logger.error("数据处理后为空，程序结束")
            return
        
        # 3. 写入目标文件
        if not write_to_target_file(processed_data):
            logger.error("数据写入失败，程序结束")
            return
        
        # 4. 添加公式（可选）
        # 计算添加数据的行范围
        wb = load_workbook(TARGET_FILE_PATH)
        ws = wb[TARGET_SHEET_NAME]
        end_row = ws.max_row
        start_row = end_row - len(processed_data) + 1
        
        logger.info(f"开始添加公式，数据行范围: {start_row} 到 {end_row}")
        add_formulas(start_row, end_row)  # 启用公式添加
        
        logger.info("=" * 50)
        logger.info("广告数据合并处理完成！")
        logger.info(f"处理数据行数: {len(processed_data)}")
        logger.info(f"目标文件: {TARGET_FILE_PATH}")
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"程序执行过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
