import os
import sys
import threading
import traceback
from pathlib import Path

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog,
    QStatusBar, QMessageBox, QGroupBox, QGridLayout
)
from PyQt6.QtCore import Qt, QObject, pyqtSignal, QThread, pyqtSlot

# 添加父目录到系统路径以导入xlsxgroup_to_sheet-v3.py
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
deal_data_dir = os.path.join(parent_dir, "deal_data")
sys.path.append(parent_dir)
sys.path.append(deal_data_dir)

# 导入数据处理脚本 - 使用importlib动态导入带有横线的模块名
import importlib.util
spec = importlib.util.spec_from_file_location(
    "xlsxgroup_to_sheet", 
    os.path.join(parent_dir, "deal_data", "xlsxgroup_to_sheet-v3.py")
)
xlsxgroup_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(xlsxgroup_module)
process_files_to_excel = xlsxgroup_module.process_files_to_excel


class WriteStream(QObject):
    """用于重定向输出到QTextEdit的类"""
    text_written = pyqtSignal(str)
    
    def write(self, text):
        self.text_written.emit(str(text))
    
    def flush(self):
        pass


class ProcessThread(QThread):
    """处理Excel文件的线程"""
    process_finished = pyqtSignal(bool)
    error_occurred = pyqtSignal(str, str)
    
    def __init__(self, source_dir, target_excel):
        super().__init__()
        self.source_dir = source_dir
        self.target_excel = target_excel
    
    def run(self):
        try:
            print(f"开始处理...\n源目录: {self.source_dir}\n目标Excel: {self.target_excel}")
            
            # 调用处理函数
            result = process_files_to_excel(self.source_dir, self.target_excel)
            
            # 发送处理结果信号
            self.process_finished.emit(result)
            
        except Exception as e:
            error_msg = f"处理过程中出错: {str(e)}"
            error_details = traceback.format_exc()
            print(error_msg)
            print(error_details)
            self.error_occurred.emit(error_msg, error_details)


class ExcelProcessorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 设置默认路径
        self.default_source_dir = os.path.abspath(os.path.join(current_dir, "../../Data/origin_data"))
        self.default_target_excel = os.path.abspath(os.path.join(current_dir, "../../Data/deal_data/广告数据对比总表.xlsx"))
        
        self.init_ui()
        
        # 重定向标准输出到文本框
        self.setup_stdout_redirect()
        
        # 检查默认路径是否存在
        self.check_default_paths()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Excel数据处理工具")
        self.setMinimumSize(800, 600)
        
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 添加源目录选择区域
        source_group = QGroupBox("源数据目录")
        source_layout = QGridLayout()
        source_group.setLayout(source_layout)
        
        source_layout.addWidget(QLabel("源目录:"), 0, 0)
        self.source_edit = QLineEdit(self.default_source_dir)
        source_layout.addWidget(self.source_edit, 0, 1)
        
        self.source_btn = QPushButton("浏览...")
        self.source_btn.clicked.connect(self.browse_source)
        source_layout.addWidget(self.source_btn, 0, 2)
        
        main_layout.addWidget(source_group)
        
        # 添加目标Excel选择区域
        target_group = QGroupBox("目标Excel文件")
        target_layout = QGridLayout()
        target_group.setLayout(target_layout)
        
        target_layout.addWidget(QLabel("目标Excel:"), 0, 0)
        self.target_edit = QLineEdit(self.default_target_excel)
        target_layout.addWidget(self.target_edit, 0, 1)
        
        self.target_btn = QPushButton("浏览...")
        self.target_btn.clicked.connect(self.browse_target)
        target_layout.addWidget(self.target_btn, 0, 2)
        
        main_layout.addWidget(target_group)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        button_layout.addWidget(self.process_btn)
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_log_btn)
        
        button_layout.addStretch()
        
        self.exit_btn = QPushButton("退出")
        self.exit_btn.clicked.connect(self.close)
        button_layout.addWidget(self.exit_btn)
        
        main_layout.addLayout(button_layout)
        
        # 添加日志区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout()
        log_group.setLayout(log_layout)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_group)
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
    
    def setup_stdout_redirect(self):
        """设置标准输出重定向"""
        self.stdout_stream = WriteStream()
        self.stdout_stream.text_written.connect(self.append_log)
    
    def check_default_paths(self):
        """检查默认路径是否存在"""
        if not os.path.exists(self.default_source_dir):
            self.log(f"警告: 默认源目录不存在: {self.default_source_dir}")
        else:
            self.log(f"默认源目录: {self.default_source_dir}")
            
        if not os.path.exists(os.path.dirname(self.default_target_excel)):
            self.log(f"警告: 默认目标Excel目录不存在: {os.path.dirname(self.default_target_excel)}")
        else:
            self.log(f"默认目标Excel: {self.default_target_excel}")
            
        self.log("请设置源目录和目标Excel文件，然后点击'开始处理'")
    
    def browse_source(self):
        """浏览并选择源目录"""
        source_dir = QFileDialog.getExistingDirectory(
            self, "选择源数据目录", self.source_edit.text()
        )
        if source_dir:
            self.source_edit.setText(source_dir)
            self.log(f"已选择源目录: {source_dir}")
    
    def browse_target(self):
        """浏览并选择目标Excel文件"""
        initial_dir = os.path.dirname(self.target_edit.text())
        target_file, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", 
            initial_dir,
            "Excel文件 (*.xlsx);;所有文件 (*.*)"
        )
        if target_file:
            if not target_file.endswith('.xlsx'):
                target_file += '.xlsx'
            self.target_edit.setText(target_file)
            self.log(f"已选择目标Excel: {target_file}")
    
    @pyqtSlot(str)
    def append_log(self, text):
        """向日志添加文本"""
        self.log_text.append(text.rstrip())
    
    def log(self, message):
        """记录消息"""
        self.log_text.append(message)
        print(message)  # 同时输出到控制台
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def start_processing(self):
        """开始处理Excel文件"""
        source_dir = self.source_edit.text()
        target_excel = self.target_edit.text()
        
        # 检查路径
        if not os.path.exists(source_dir):
            QMessageBox.critical(self, "错误", f"源目录不存在: {source_dir}")
            return
        
        # 确保目标Excel的目录存在
        target_dir = os.path.dirname(target_excel)
        if target_dir and not os.path.exists(target_dir):
            try:
                os.makedirs(target_dir)
                self.log(f"已创建目标目录: {target_dir}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法创建目标目录: {str(e)}")
                return
        
        # 禁用按钮，防止重复点击
        self.process_btn.setEnabled(False)
        self.statusBar().showMessage("处理中...")
        
        # 重定向标准输出
        self.old_stdout = sys.stdout
        sys.stdout = self.stdout_stream
        
        # 创建处理线程
        self.process_thread = ProcessThread(source_dir, target_excel)
        self.process_thread.process_finished.connect(self.on_process_finished)
        self.process_thread.error_occurred.connect(self.on_error_occurred)
        self.process_thread.finished.connect(self.on_thread_finished)
        self.process_thread.start()
    
    @pyqtSlot(bool)
    def on_process_finished(self, success):
        """处理完成后的回调"""
        if success:
            self.log("处理成功完成!")
            QMessageBox.information(self, "成功", "Excel文件处理成功!")
        else:
            self.log("处理失败!")
            QMessageBox.critical(self, "失败", "Excel文件处理失败!")
    
    @pyqtSlot(str, str)
    def on_error_occurred(self, error_msg, error_details):
        """处理错误回调"""
        QMessageBox.critical(self, "错误", error_msg)
    
    def on_thread_finished(self):
        """线程结束回调"""
        # 恢复标准输出
        sys.stdout = self.old_stdout
        
        # 恢复按钮状态
        self.process_btn.setEnabled(True)
        self.statusBar().showMessage("就绪")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 确保关闭前恢复标准输出
        if hasattr(self, 'old_stdout') and sys.stdout != self.old_stdout:
            sys.stdout = self.old_stdout
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ExcelProcessorApp()
    window.show()
    sys.exit(app.exec()) 