# excel整理、按列名对应添加数据到工作表（简化版v3）
import pandas as pd
import os
import openpyxl
import warnings
from datetime import datetime
import shutil
import time
import re
import traceback  # 添加跟踪模块

# 忽略特定的弃用警告
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
warnings.filterwarnings('ignore', category=DeprecationWarning)

def read_excel_file(file_path):
    """
    尝试以多种方式读取Excel文件，找到最合适的数据结构
    """
    print(f"读取文件: {file_path}")
    
    try:
        # 跳过第一行（汇总行）
        df = pd.read_excel(file_path, skiprows=1)
        print(f"跳过第一行读取成功，列名: {df.columns.tolist()}")
        
        # 打印数据的前几行，便于调试
        print("数据预览:")
        print(df.head(1))
        
        # 检查是否需要特别处理（没有找到有用的列名）
        if all('Unnamed' in str(col) for col in df.columns if isinstance(col, str)):
            print("未检测到有效列名，尝试读取其他行作为标题...")
            # 尝试读取第二行作为标题
            df = pd.read_excel(file_path, header=1, skiprows=1)
            print(f"使用第2行作为标题，列名: {df.columns.tolist()}")
            print(df.head(1))
            
        return df
    except Exception as e:
        print(f"读取文件失败: {e}")
        traceback.print_exc()  # 打印详细错误
        return pd.DataFrame()  # 返回空DataFrame

# 修改extract_number函数，添加参数使其可以返回整数类型
def extract_number(text, as_int=True):
    """
    从文本中提取数字部分
    as_int: 如果为True，则将结果转为整数，去掉前导零
    """
    if not isinstance(text, str):
        if as_int and isinstance(text, (int, float)):
            return int(text)
        return text
    
    numbers = re.findall(r'\d+', text)
    if not numbers:
        return text
    
    # 连接数字字符
    result = ''.join(numbers)
    
    # 尝试转为整数以去掉前导零
    if as_int:
        try:
            return int(result)
        except ValueError:
            return result
    
    return result


# 修改日期格式化函数，去掉前导零
def format_date(date_value):
    """将日期格式化为 yyyy/m/d 格式(没有前导零)"""
    if not date_value:
        return date_value
    
    try:
        if isinstance(date_value, datetime):
            # 使用%-格式化移除前导零（在Windows上可能不起作用，需要特殊处理）
            try:
                return date_value.strftime('%Y/%-m/%-d')  # Linux/Mac格式
            except ValueError:
                # Windows格式处理
                month = str(date_value.month)
                day = str(date_value.day)
                return f"{date_value.year}/{month}/{day}"
        elif isinstance(date_value, str):
            # 尝试解析日期字符串
            date_obj = datetime.strptime(date_value, '%Y-%m-%d')
            try:
                return date_obj.strftime('%Y/%-m/%-d')  # Linux/Mac格式
            except ValueError:
                # Windows格式处理
                month = str(date_obj.month)
                day = str(date_obj.day)
                return f"{date_obj.year}/{month}/{day}"
        return date_value
    except:
        return date_value  # 如果转换失败，返回原值

def ensure_formula_has_equals(formula):
    """确保公式以等号开头"""
    if formula and isinstance(formula, str) and not formula.startswith('='):
        return '=' + formula
    return formula

def find_last_data_row(worksheet, min_check=10):
    """
    查找最后一个非空行，通过检查前几列的实际数据
    min_check: 最小需要检查的列数
    """
    print("查找最后一个实际数据行...")
    
    # 获取工作表的维度
    max_row = worksheet.max_row
    max_col = min(worksheet.max_column, 15)  # 最多检查前15列
    
    # 关键列 - 确保这些列包含实际数据(时间、广告名称、广告账户等)
    key_columns = [2, 5]  # 时间和广告名称列
    
    # 从最后一行开始向上查找有实际数据的行
    for row in range(max_row, 1, -1):
        has_data = False
        for col in range(1, max_col + 1):
            cell = worksheet.cell(row=row, column=col)
            if cell.value is not None and cell.value != "":
                # 如果单元格不是公式或公式已经计算过有值
                if cell.data_type != 'f' or (isinstance(cell.value, (int, float)) and col in key_columns):
                    print(f"在行 {row}, 列 {col} 找到数据: {cell.value}")
                    has_data = True
                    break
        if has_data:
            return row
    
    # 如果没有找到数据行，返回标题行
    return 1

def get_numeric_columns(data, top_n=10):
    """
    分析数据框，找出包含数值的列并按大小排序
    返回按平均值排序的列名列表
    """
    numeric_cols = {}
    
    # 尝试将每一列转换为数值型
    for col in data.columns:
        try:
            # 取平均值，忽略NaN
            values = pd.to_numeric(data[col], errors='coerce')
            if not values.isna().all():  # 如果列中有非NaN值
                mean_val = values.mean()
                if not pd.isna(mean_val):
                    numeric_cols[col] = mean_val
                    print(f"列 {col} 平均值: {mean_val}")
        except:
            pass
    
    # 按值大小排序
    sorted_cols = sorted(numeric_cols.items(), key=lambda x: x[1], reverse=True)
    return sorted_cols[:top_n]

# 修改process_files_to_excel函数，添加排序处理
def process_files_to_excel(source_dir, target_excel, target_sheet='台账'):
    """
    处理源目录中的所有Excel文件，并将数据添加到目标Excel的指定工作表
    改进版：分别处理每个Excel文件，避免合并后列名混乱的问题
    """
    # 1. 获取所有Excel文件
    excel_files = [f for f in os.listdir(source_dir) if f.endswith('.xlsx') and not f.startswith('~$')]
    if not excel_files:
        print("未找到Excel文件!")
        return False
    
    print(f"找到 {len(excel_files)} 个Excel文件: {excel_files}")
    
    # 2. 创建工作簿备份
    backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = target_excel.replace(".xlsx", f"_backup_{backup_time}.xlsx")
    
    if os.path.exists(target_excel):
        try:
            shutil.copy2(target_excel, backup_path)
            print(f"已创建备份文件：{backup_path}")
        except Exception as e:
            print(f"创建备份时出错: {e}")
            print("尝试继续处理...")
    
    # 3. 加载目标工作簿
    print("正在加载目标工作簿...")
    try_count = 0
    while try_count < 3:
        try:
            try_count += 1
            workbook = openpyxl.load_workbook(target_excel)
            print("目标工作簿加载成功")
            break
        except Exception as e:
            if try_count < 3:
                print(f"加载工作簿失败，尝试再次加载: {e}")
                time.sleep(2)  # 等待2秒再试
            else:
                print(f"多次尝试后仍无法加载工作簿: {e}")
                return False
    
    try:
        # 4. 创建或更新唯一广告名称工作表
        # 为此我们需要先读取所有文件的广告名称数据
        all_ad_names = []
        for file in excel_files:
            file_path = os.path.join(source_dir, file)
            df = read_excel_file(file_path)
            if not df.empty:
                # 尝试从不同可能的列名获取广告名称
                possible_ad_name_cols = ['广告名称', '广告', 'Unnamed: 2', 2]
                for col in possible_ad_name_cols:
                    try:
                        if col in df.columns:
                            names = df[col].dropna().unique().tolist()
                            all_ad_names.extend(names)
                            print(f"从文件 {file} 获取了 {len(names)} 个广告名称")
                            break
                    except:
                        continue
        
        # 去重
        all_ad_names = list(set(all_ad_names))
        print(f"总共收集到 {len(all_ad_names)} 个唯一广告名称")
        
        # 更新唯一广告名称工作表
        fast_create_unique_ad_name_sheet(workbook, all_ad_names)
        
        # 5. 查找工作表最后一行
        if target_sheet in workbook.sheetnames:
            worksheet = workbook[target_sheet]
            last_row = find_last_data_row(worksheet)
            print(f"台账最后一行数据: {last_row}")
        else:
            # 创建新工作表
            worksheet = workbook.create_sheet(target_sheet)
            
            # 添加表头
            headers = [
                '广告账户', '时间', '广告系列', '广告组', '广告名称', '短剧编号',
                '展示次数', '千次展示费用 (USD)', '点击量（全部）', '调整决策', '成效',
                '点击注册转化率', '展示注册转化率', '点击率', '单次注册成本', '已花费金额 (USD)'
            ]
            for idx, header in enumerate(headers):
                worksheet.cell(row=1, column=idx+1, value=header)
            
            last_row = 1
            print(f"已创建表头: {headers}")
        
        # 6. 收集所有数据，之后一次性排序后添加
        all_processed_data = []
        
        for file in excel_files:
            file_path = os.path.join(source_dir, file)
            print(f"\n开始处理文件: {file}")
            
            df = read_excel_file(file_path)
            if df.empty:
                print(f"文件 {file} 没有有效数据，跳过")
                continue
            
            # 过滤空行
            filtered_data = filter_empty_rows(df)
            print(f"文件 {file} 过滤后数据行数: {len(filtered_data)}")
            
            if len(filtered_data) == 0:
                print(f"文件 {file} 过滤后没有数据，跳过")
                continue
            
            # 分析数值列
            numeric_cols = get_numeric_columns(filtered_data)
            print(f"文件 {file} 数值列排序: {numeric_cols}")
            
            # 为每个文件的数据处理列映射和转换
            processed_data = process_data_for_import(filtered_data, numeric_cols, file)
            if processed_data:
                all_processed_data.extend(processed_data)
                print(f"文件 {file} 处理了 {len(processed_data)} 行数据")
        
        # 按广告账户从小到大排序
        if all_processed_data:
            print("对所有数据按广告账户从小到大排序...")
            # 尝试将广告账户直接用于排序，因为已经在process_data_for_import中处理为整数
            for item in all_processed_data:
                try:
                    if '广告账户' in item:
                        # 如果已经是整数，不需要再转换
                        item['广告账户_排序'] = int(item['广告账户']) if not isinstance(item['广告账户'], int) else item['广告账户']
                    else:
                        item['广告账户_排序'] = 999
                except:
                    item['广告账户_排序'] = 999  # 如果无法转换为数字，则放到最后
            
            # 排序
            all_processed_data.sort(key=lambda x: x['广告账户_排序'])
            print(f"排序后的广告账户顺序: {[item['广告账户'] for item in all_processed_data[:5]]}")
            
            # 处理台账工作表 - 使用高效合并功能
            print("开始快速合并数据...")
            rows_processed = fast_merge_data_to_sheet(workbook, all_processed_data, target_sheet)
            print(f"处理完成: {rows_processed} 条数据")
        else:
            print("没有数据需要添加")
        
        # 7. 保存工作簿
        print("正在保存工作簿...")
        try_count = 0
        while try_count < 3:
            try:
                try_count += 1
                workbook.save(target_excel)
                print(f"数据已成功保存到 {target_excel}")
                break
            except PermissionError as e:
                if try_count < 3:
                    print(f"保存失败，文件可能被占用。尝试另存为新文件: {e}")
                    # 尝试另存为新文件
                    new_file = target_excel.replace(".xlsx", f"_new_{backup_time}.xlsx")
                    workbook.save(new_file)
                    print(f"数据已保存到新文件: {new_file}")
                    break
                else:
                    print(f"多次尝试后仍无法保存: {e}")
                    return False
                    
        return True
    
    except Exception as e:
        print(f"处理工作簿时出错: {e}")
        traceback.print_exc()  # 打印详细错误
        return False


def fast_create_unique_ad_name_sheet(workbook, ad_names, sheet_name='唯一广告名称'):
    """
    快速版本：创建或更新唯一广告名称工作表
    """
    if not ad_names:
        return

    try:
        if sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]

            # 快速读取现有名称
            existing_names = set()
            for row in range(2, worksheet.max_row + 1):
                cell_value = worksheet.cell(row=row, column=1).value
                if cell_value:
                    existing_names.add(cell_value)

            # 批量添加新名称
            new_names = [name for name in ad_names if name not in existing_names]
            if new_names:
                start_row = worksheet.max_row + 1
                for idx, name in enumerate(new_names):
                    worksheet.cell(row=start_row + idx, column=1, value=name)
                print(f"添加了 {len(new_names)} 个新广告名称")
        else:
            # 创建新工作表
            worksheet = workbook.create_sheet(sheet_name)
            worksheet.cell(row=1, column=1, value='唯一广告名称')
            worksheet.cell(row=1, column=2, value='短剧编号')

            for idx, name in enumerate(ad_names):
                worksheet.cell(row=idx + 2, column=1, value=name)
            print(f"创建工作表，包含 {len(ad_names)} 个广告名称")

    except Exception as e:
        print(f"处理唯一广告名称出错: {e}")


def update_cell_reference(formula, row_diff):
    """
    更新单元格引用，例如将E977更新为E(977+row_diff)
    """
    # 匹配单元格引用，例如E977
    pattern = r'([A-Z]+)(\d+)'
    
    def replace_ref(match):
        col = match.group(1)  # 列标识，如E
        row = int(match.group(2))  # 行号，如977
        return f"{col}{row + row_diff}"
    
    return re.sub(pattern, replace_ref, formula)

def update_formula_references(formula, current_row):
    """
    更新公式中的行引用，使其引用当前行
    current_row: 当前要插入的行号
    """
    # 确保公式有等号
    formula = ensure_formula_has_equals(formula)
    
    # 处理VLOOKUP公式，例如将VLOOKUP(E1073,...)改为VLOOKUP(E1074,...)
    if "VLOOKUP" in formula:
        # 匹配VLOOKUP的第一个参数中的行引用，如VLOOKUP(E1073,...)中的E1073
        vlookup_pattern = r'(VLOOKUP\()([A-Z]+)(\d+)(,.+)'
        match = re.search(vlookup_pattern, formula)
        if match:
            col = match.group(2)  # 列标识，如E
            # 替换为当前行号
            formula = match.group(1) + col + str(current_row) + match.group(4)
    
    # 处理简单公式，如I1073/G1073等
    simple_patterns = [
        r'([A-Z]+)(\d+)(/[A-Z]+)(\d+)',  # 如 I1073/G1073
        r'([A-Z]+)(\d+)(\*[A-Z]+)(\d+)',  # 如 P1073*G1073
        r'([A-Z]+)(\d+)(\+[A-Z]+)(\d+)',  # 如 P1073+G1073
        r'([A-Z]+)(\d+)(-[A-Z]+)(\d+)'   # 如 P1073-G1073
    ]
    
    for pattern in simple_patterns:
        if re.search(pattern, formula):
            # 使用函数替换所有单元格引用
            def replace_cell_ref(match):
                col = match.group(1)
                return col + str(current_row)
            
            # 替换公式中的所有单元格引用
            formula = re.sub(r'([A-Z]+)\d+', replace_cell_ref, formula)
    
    return formula

# 修改过滤空行函数，让它能够返回更多诊断信息
def filter_empty_rows(data):
    """
    过滤掉空数据行和第一行汇总行，确保只添加有实际数据的行
    """
    # 跳过第一行汇总行
    if len(data) > 1:
        filtered_data = data.iloc[1:].copy()  # 从第二行开始
        print(f"跳过第一行汇总行，从 {len(data)} 行减少到 {len(filtered_data)} 行")
    else:
        filtered_data = data

    # 查找关键列进行空行过滤
    key_columns = []
    for col in ['广告名称', '展示次数', '点击量（全部）', '帐户名称', '账户名称', 'Unnamed: 3', 3]:
        if col in filtered_data.columns:
            key_columns.append(col)

    # 过滤空行
    if key_columns:
        original_len = len(filtered_data)
        filtered_data = filtered_data.dropna(subset=key_columns, how='all')
        if len(filtered_data) < original_len:
            print(f"过滤掉 {original_len - len(filtered_data)} 行空数据")

    print(f"最终保留 {len(filtered_data)} 行有效数据")
    return filtered_data

# 修改处理台账函数中的列映射逻辑
def process_ledger_sheet(workbook, data, sheet_name='台账', numeric_cols=None, start_row=None):
    """
    处理台账工作表 - 增强版
    修改版：接收起始行参数
    """
    try:
        print(f"处理工作表 '{sheet_name}'")
        
        # 检查工作表是否存在
        if sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            print(f"找到工作表 '{sheet_name}'")
            
            # 获取表头信息
            headers = []
            column_mapping = {}
            for col in range(1, worksheet.max_column + 1):
                header = worksheet.cell(row=1, column=col).value
                if header:
                    headers.append(header)
                    column_mapping[header] = col
            
            print(f"表头: {headers}")
            
            # 使用传入的起始行或者查找最后一行数据
            if start_row is None:
                last_row = find_last_data_row(worksheet)
                print(f"最后一行数据: {last_row}")
                start_row = last_row + 1
            
        else:
            # 创建新工作表
            worksheet = workbook.create_sheet(sheet_name)
            
            # 添加表头
            headers = [
                '广告账户', '时间', '广告系列', '广告组', '广告名称', '短剧编号',
                '展示次数', '千次展示费用 (USD)', '点击量（全部）', '调整决策', '成效',
                '点击注册转化率', '展示注册转化率', '点击率', '单次注册成本', '已花费金额 (USD)'
            ]
            for idx, header in enumerate(headers):
                worksheet.cell(row=1, column=idx+1, value=header)
            
            column_mapping = {header: idx+1 for idx, header in enumerate(headers)}
            start_row = 2
            print(f"已创建表头: {headers}")
        
        print(f"从第 {start_row} 行开始添加数据")
        
        # 检查数据列
        print("数据列预览:")
        for col in data.columns:
            try:
                preview = data[col].iloc[0]
                print(f"列 {col}: {preview}")
            except:
                print(f"列 {col}: 无法获取预览")
        
        # 创建列映射
        column_mappings = {}
        
        # 1. 尝试通过列名映射
        column_names_to_find = {
            '广告账户': ['帐户名称', '账户名称', '账户', 'Unnamed: 3', 3],
            '时间': ['报告结束日期', '报告日期', '日期', 'Unnamed: 13', 13, '2025-07-17', '2025-07-17.1', '2025-07-31', '2025-07-31.1'],
            '广告名称': ['广告名称', '广告', 'Unnamed: 2', 2],
            '展示次数': ['展示次数', '展示量', 'Unnamed: 5', 5, 20158, 38870],
            '点击量（全部）': ['点击量（全部）', '点击量', 'Unnamed: 10', 10, 1755, 4980],
            '成效': ['网站完成注册次数', '网站注册费用', '成效', 'Unnamed: 9', 9, 58, 110, 0.43844828],
            '已花费金额 (USD)': ['已花费金额 (USD)', '已花费金额', '花费', 'Unnamed: 7', 7, 25.43, 50.19, 'USD']
        }
        
        for target_col, possible_cols in column_names_to_find.items():
            for col in possible_cols:
                if col in data.columns:
                    column_mappings[target_col] = col
                    print(f"通过列名匹配: '{target_col}' -> '{col}'")
                    break
        
        # 2. 使用数值列信息来猜测
        if numeric_cols and len(numeric_cols) > 0:
            # 按值从大到小排序
            for i, (col, val) in enumerate(numeric_cols):
                if '展示次数' not in column_mappings and i == 0:
                    column_mappings['展示次数'] = col
                    print(f"通过数值大小猜测: '展示次数' -> '{col}' (最大值: {val})")
                elif '点击量（全部）' not in column_mappings and i == 1:
                    column_mappings['点击量（全部）'] = col
                    print(f"通过数值大小猜测: '点击量（全部）' -> '{col}' (第二大值: {val})")
                elif '已花费金额 (USD)' not in column_mappings and 1 < i < len(numeric_cols) - 1:
                    column_mappings['已花费金额 (USD)'] = col
                    print(f"通过数值大小猜测: '已花费金额 (USD)' -> '{col}' (中间值: {val})")
                elif '成效' not in column_mappings and i == len(numeric_cols) - 1:
                    column_mappings['成效'] = col
                    print(f"通过数值大小猜测: '成效' -> '{col}' (最小值: {val})")
        
        # 3. 广告账户特殊处理 - 检查'Unnamed: 3'列的内容
        if '广告账户' not in column_mappings and 'Unnamed: 3' in data.columns:
            column_mappings['广告账户'] = 'Unnamed: 3'
            print("特殊处理: 使用'Unnamed: 3'列作为广告账户")
        
        # 4. 处理已花费金额特殊情况
        if '已花费金额 (USD)' not in column_mappings:
            # 如果有USD列，检查它是否是字符串列
            if 'USD' in data.columns:
                if data['USD'].dtype == 'object':
                    # 通常USD列是货币单位，不是实际金额
                    print("'USD'列可能是货币单位而非金额，继续查找...")
                else:
                    # 如果是数值，可能是金额
                    column_mappings['已花费金额 (USD)'] = 'USD'
                    print("使用'USD'列作为已花费金额")
            
            # 查找其他可能是花费的数值列
            for col in [25.43, 50.19]:
                if col in data.columns:
                    column_mappings['已花费金额 (USD)'] = col
                    print(f"使用数值列{col}作为已花费金额")
                    break
        
        # 5. 检查是否找到所有必要的列
        missing_cols = [col for col in ['广告名称', '展示次数', '点击量（全部）', '成效', '已花费金额 (USD)', '广告账户'] 
                      if col not in column_mappings]
        if missing_cols:
            print(f"警告: 未找到以下列的映射: {missing_cols}")
            
        # 打印最终的列映射结果
        print("最终列映射结果:")
        for target_col, source_col in column_mappings.items():
            print(f"  {target_col} <- {source_col}")
        
        # 定义公式列及其模板
        formula_templates = {
            '广告系列': '=VLOOKUP(E{row},Sheet2!C:E,2,0)',
            '广告组': '=VLOOKUP(E{row},Sheet2!C:E,3,0)',
            '短剧编号': '=VLOOKUP(E{row},唯一广告名称!A:B,2,0)',
            '千次展示费用 (USD)': '=IFERROR(P{row}/G{row}*1000,0)',
            '点击注册转化率': '=IFERROR(K{row}/I{row},0)',
            '展示注册转化率': '=IFERROR(K{row}/G{row},0)',
            '点击率': '=I{row}/G{row}',
            '单次注册成本': '=IFERROR(P{row}/K{row},0)'
        }
        
        # 添加数据
        rows_added = 0
        print(f"开始添加 {len(data)} 行数据...")
        
        for idx, (_, row_data) in enumerate(data.iterrows()):
            current_row = start_row + idx
            
            # 添加基础数据列
            for target_col, source_col in column_mappings.items():
                # 跳过公式列
                if target_col in formula_templates:
                    continue
                
                if target_col in column_mapping:
                    col_idx = column_mapping[target_col]
                    try:
                        value = row_data.get(source_col)
                        
                        # 特殊处理
                        if target_col == '广告账户' and isinstance(value, str):
                            value = extract_number(value)
                        elif target_col == '时间':
                            value = format_date(value)
                            
                        # 防止None值
                        if value is None:
                            print(f"警告: 行{current_row}的{target_col}列值为None，尝试再次获取")
                            # 尝试直接用索引获取
                            if isinstance(source_col, int) or (isinstance(source_col, str) and source_col.isdigit()):
                                try:
                                    col_idx_in_df = int(source_col)
                                    if col_idx_in_df < len(row_data):
                                        value = row_data.iloc[col_idx_in_df]
                                        print(f"通过索引{col_idx_in_df}获取值: {value}")
                                except:
                                    print(f"无法通过索引{source_col}获取值")
                            
                            # 如果还是None，则跳过
                            if value is None:
                                continue
                        
                        # 字符串"nan"转为空值
                        if isinstance(value, str) and value.lower() == 'nan':
                            value = None
                            
                        worksheet.cell(row=current_row, column=col_idx, value=value)
                        
                        if idx < 3:  # 打印前三行的详情
                            print(f"写入: 行={current_row}, 列={col_idx} ({target_col}), 值={value}")
                    except Exception as e:
                        print(f"写入 {target_col} 时出错: {e}")
                        print(f"行数据键: {row_data.keys() if hasattr(row_data, 'keys') else '无键方法'}")
                        print(f"尝试获取的列: {source_col}")
                        
            # 添加公式列
            for target_col, formula_template in formula_templates.items():
                if target_col in column_mapping:
                    col_idx = column_mapping[target_col]
                    try:
                        # 为第一行使用原始值
                        if idx == 0 and (target_col == '广告系列' or target_col == '广告组'):
                            source_col = column_mappings.get(target_col)
                            if source_col and source_col in row_data:
                                worksheet.cell(row=current_row, column=col_idx, value=row_data[source_col])
                                continue
                        
                        # 为当前行生成正确的公式
                        formula = formula_template.format(row=current_row)
                        
                        # 确保公式以等号开头
                        formula = ensure_formula_has_equals(formula)
                        
                        worksheet.cell(row=current_row, column=col_idx, value=formula)
                        
                        if idx < 3:  # 打印前三行详情
                            print(f"公式: 行={current_row}, 列={col_idx} ({target_col}), 公式={formula}")
                    except Exception as e:
                        print(f"写入公式 {target_col} 时出错: {e}")
                        traceback.print_exc()
                        
            rows_added += 1
            if rows_added % 10 == 0:
                print(f"已添加 {rows_added}/{len(data)} 行...")
        
        print(f"成功添加 {rows_added} 行数据")
        return rows_added
    except Exception as e:
        print(f"处理台账工作表时出错: {e}")
        traceback.print_exc()
        return 0

# 修改处理数据函数中的列映射逻辑
def process_data_for_import(data, numeric_cols=None, source_file=""):
    """
    处理数据，转换为标准格式，准备导入 - 改进版
    返回处理后的字典列表，每个字典代表一行数据
    """
    try:
        # 使用改进的列映射函数
        column_mappings = improved_column_mapping(data, source_file)

        # 如果映射失败，返回空列表
        if not column_mappings:
            print(f"❌ 文件 {source_file} 列映射失败，跳过处理")
            return []

        # 转换数据为标准格式
        processed_data = []

        for idx, (_, row_data) in enumerate(data.iterrows()):
            row_dict = {'来源文件': source_file}

            # 提取基础数据列
            for target_col, source_col in column_mappings.items():
                try:
                    value = row_data.get(source_col)

                    # 特殊处理
                    if target_col == '广告账户' and (isinstance(value, str) or isinstance(value, (int, float))):
                        value = extract_number(value, as_int=True)
                    elif target_col == '时间':
                        value = format_date(value)
                    elif target_col == '已花费金额 (USD)':
                        value = clean_currency_value(value)
                    elif target_col in ['成效', '展示次数', '点击量（全部）']:
                        if value is not None:
                            try:
                                value = float(value) if not pd.isna(value) else None
                            except:
                                print(f"⚠️ 无法转换 {target_col} 的值 '{value}' 为数值")
                                value = None

                    # 字符串"nan"转为空值
                    if isinstance(value, str) and value.lower() == 'nan':
                        value = None

                    row_dict[target_col] = value

                except Exception as e:
                    print(f"❌ 处理 {target_col} 时出错: {e}")
                    row_dict[target_col] = None

            processed_data.append(row_dict)

        print(f"✅ 成功处理 {len(processed_data)} 行数据")
        return processed_data

    except Exception as e:
        print(f"❌ 处理数据时出错: {e}")
        traceback.print_exc()
        return []

def clean_currency_value(value):
    """
    清理货币数值，移除USD等货币符号
    """
    if pd.isna(value) or value is None:
        return value

    # 转换为字符串进行处理
    str_value = str(value).strip()

    # 如果已经是数值，直接返回
    try:
        return float(str_value)
    except ValueError:
        pass

    # 移除常见的货币符号和格式
    currency_symbols = ['USD', 'CNY', 'RMB', '$', '¥', '€', '£', '￥']
    for symbol in currency_symbols:
        str_value = str_value.replace(symbol, '')

    # 移除逗号分隔符
    str_value = str_value.replace(',', '')

    # 移除多余的空格
    str_value = str_value.strip()

    # 尝试转换为数值
    try:
        return float(str_value)
    except ValueError:
        return value  # 如果无法转换，返回原值

def get_corrected_column_mapping():
    """
    返回修正后的列映射配置
    基于实际Excel文件结构和用户提供的正确映射关系
    """
    return {
        '广告账户': ['帐户名称', '账户名称', '账户', 'Unnamed: 3'],
        '时间': ['报告结束日期', '报告日期', '日期', 'Unnamed: 13', '2025-07-31', '2025-07-31.1'],
        '广告名称': ['广告名称', 'Unnamed: 2'],  # 根据实际结构添加
        '展示次数': ['展示次数', '展示量', 'Unnamed: 5'],  # 将通过数值大小推测
        '点击量（全部）': ['点击量（全部）'],  # 将通过数值大小推测
        '成效': ['网站完成注册次数'],  # 将通过数值大小推测
        '已花费金额 (USD)': ['已花费金额 (USD)']  # 将通过数值大小推测
    }

def validate_column_mapping(data, column_mappings):
    """
    验证列映射的正确性
    """
    validation_results = {}

    for target_col, source_col in column_mappings.items():
        if source_col not in data.columns:
            validation_results[target_col] = {
                'status': 'ERROR',
                'message': f'源列 {source_col} 不存在'
            }
            continue

        # 检查数据样本
        sample_data = data[source_col].dropna().head(3).tolist()

        # 数据类型验证
        if target_col in ['展示次数', '点击量（全部）', '成效']:
            # 应该是数值类型
            numeric_count = sum(1 for x in sample_data if pd.api.types.is_numeric_dtype(type(x)))
            if numeric_count == 0:
                validation_results[target_col] = {
                    'status': 'WARNING',
                    'message': f'预期数值类型，但样本数据为: {sample_data}'
                }
            else:
                validation_results[target_col] = {
                    'status': 'OK',
                    'message': f'数值类型验证通过，样本: {sample_data}'
                }
        elif target_col == '广告名称':
            # 应该是文本类型
            if all(isinstance(x, str) for x in sample_data):
                validation_results[target_col] = {
                    'status': 'OK',
                    'message': f'文本类型验证通过，样本: {sample_data}'
                }
            else:
                validation_results[target_col] = {
                    'status': 'WARNING',
                    'message': f'预期文本类型，但样本数据为: {sample_data}'
                }
        else:
            validation_results[target_col] = {
                'status': 'OK',
                'message': f'映射成功，样本: {sample_data}'
            }

    return validation_results

def print_validation_results(validation_results):
    """
    打印验证结果
    """
    print("\n=== 列映射验证结果 ===")
    for target_col, result in validation_results.items():
        status_icon = "✅" if result['status'] == 'OK' else "⚠️" if result['status'] == 'WARNING' else "❌"
        print(f"{status_icon} {target_col}: {result['message']}")
    print("=" * 50)

def analyze_numeric_columns(data):
    """
    分析数据框中的数值列，返回详细的列信息
    """
    numeric_cols = []

    for col in data.columns:
        try:
            # 尝试将列转换为数值型
            values = pd.to_numeric(data[col], errors='coerce')
            if not values.isna().all():  # 如果列中有非NaN值
                mean_val = values.mean()
                max_val = values.max()
                min_val = values.min()
                std_val = values.std()

                if not pd.isna(mean_val):
                    col_info = {
                        'column': col,
                        'mean': mean_val,
                        'max': max_val,
                        'min': min_val,
                        'std': std_val,
                        'range': max_val - min_val,
                        'has_decimals': any(values % 1 != 0),
                        'sample_values': values.dropna().head(3).tolist()
                    }
                    numeric_cols.append(col_info)
                    print(f"数值列 {col}: 平均={mean_val:.2f}, 最大={max_val}, 最小={min_val}, 标准差={std_val:.2f}")
        except:
            pass

    # 按平均值从大到小排序
    numeric_cols.sort(key=lambda x: x['mean'], reverse=True)
    return numeric_cols

def smart_numeric_mapping(target_column, numeric_cols, data=None):
    """
    基于数值特征智能映射列 - 重写版
    data参数保留以兼容调用，但当前版本中不使用
    """
    if not numeric_cols:
        return None

    print(f"\n🔍 为 '{target_column}' 寻找最佳数值列匹配...")

    # 根据目标列的特征进行映射
    if target_column == '展示次数':
        # 展示次数特征：通常是最大的数值，整数，范围很大
        for col_info in numeric_cols:
            if col_info['mean'] > 100:  # 展示次数通常很大
                print(f"  ✅ 展示次数映射到: {col_info['column']} (平均值: {col_info['mean']:.2f})")
                return col_info['column']
        # 如果没有找到大数值，取最大的
        if numeric_cols:
            print(f"  ⚠️ 展示次数默认映射到最大数值列: {numeric_cols[0]['column']}")
            return numeric_cols[0]['column']

    elif target_column == '点击量（全部）':
        # 点击量特征：中等大小的数值，通常比展示次数小，比成效大
        for col_info in numeric_cols:
            if 10 <= col_info['mean'] <= 200:  # 点击量通常在这个范围
                print(f"  ✅ 点击量映射到: {col_info['column']} (平均值: {col_info['mean']:.2f})")
                return col_info['column']
        # 如果没有找到合适的，取第二大的
        if len(numeric_cols) > 1:
            print(f"  ⚠️ 点击量默认映射到第二大数值列: {numeric_cols[1]['column']}")
            return numeric_cols[1]['column']

    elif target_column == '成效':
        # 成效特征：较小的数值，通常是个位数
        for col_info in numeric_cols:
            if col_info['mean'] <= 10:  # 成效通常很小
                print(f"  ✅ 成效映射到: {col_info['column']} (平均值: {col_info['mean']:.2f})")
                return col_info['column']
        # 如果没有找到小数值，取最小的
        if numeric_cols:
            smallest = min(numeric_cols, key=lambda x: x['mean'])
            print(f"  ⚠️ 成效默认映射到最小数值列: {smallest['column']}")
            return smallest['column']

    elif target_column == '已花费金额 (USD)':
        # 已花费金额特征：有小数且范围在0-15的数值列
        for col_info in numeric_cols:
            # 寻找有小数且数值在0-15范围内的列
            if (col_info['has_decimals'] and
                0 <= col_info['mean'] <= 15):  # 费用通常在0-15范围
                print(f"  ✅ 已花费金额映射到: {col_info['column']} (平均值: {col_info['mean']:.2f}, 有小数: {col_info['has_decimals']})")
                return col_info['column']

        # 如果没有找到有小数的，寻找0-15范围的数值
        for col_info in numeric_cols:
            if 0 <= col_info['mean'] <= 15:
                print(f"  ⚠️ 已花费金额映射到0-15范围数值列: {col_info['column']} (平均值: {col_info['mean']:.2f})")
                return col_info['column']

        # 最后尝试：取第三大的数值列
        if len(numeric_cols) >= 3:
            print(f"  ⚠️ 已花费金额默认映射到第三大数值列: {numeric_cols[2]['column']}")
            return numeric_cols[2]['column']

    print(f"  ❌ 未找到合适的映射列")
    return None

def find_best_column_match(available_columns, target_column):
    """
    智能匹配最相似的列名
    """
    try:
        from difflib import get_close_matches

        # 将所有列名转换为字符串进行匹配
        str_columns = [str(col) for col in available_columns]
        matches = get_close_matches(target_column, str_columns, n=1, cutoff=0.6)
        if matches:
            # 找到匹配的原始列名
            for i, str_col in enumerate(str_columns):
                if str_col == matches[0]:
                    return available_columns[i]
    except ImportError:
        pass

    # 尝试关键词匹配
    keywords = {
        '成效': ['注册', '转化', '成效'],
        '点击量（全部）': ['点击', 'click'],
        '已花费金额 (USD)': ['花费', '金额', 'USD', '费用'],
        '广告名称': ['广告', '名称', 'name']
    }

    if target_column in keywords:
        for col in available_columns:
            col_str = str(col).lower()
            for keyword in keywords[target_column]:
                if keyword.lower() in col_str:
                    return col

    return None

def improved_column_mapping(data, source_file=""):
    """
    改进的列映射函数，包含验证和错误处理
    """
    print(f"\n开始处理文件: {source_file}")
    print(f"数据形状: {data.shape}")
    print(f"列名: {data.columns.tolist()}")

    # 获取修正后的列映射配置
    column_names_to_find = get_corrected_column_mapping()

    # 执行列映射
    column_mappings = {}
    for target_col, possible_cols in column_names_to_find.items():
        for col in possible_cols:
            if col in data.columns:
                column_mappings[target_col] = col
                print(f"✅ 映射成功: '{target_col}' -> '{col}'")
                break
        else:
            print(f"❌ 未找到映射: '{target_col}' (尝试的列: {possible_cols})")

    # 分析数值列，用于智能映射
    numeric_cols = analyze_numeric_columns(data)

    # 检查必要列是否都已映射
    required_columns = ['广告名称', '点击量（全部）', '成效', '已花费金额 (USD)']
    missing_required = [col for col in required_columns if col not in column_mappings]

    if missing_required:
        print(f"⚠️ 警告: 缺少必要列的映射: {missing_required}")
        print("尝试基于数值大小进行智能映射...")

        # 基于数值大小进行智能映射
        if numeric_cols:
            for missing_col in missing_required:
                best_match = smart_numeric_mapping(missing_col, numeric_cols, data)
                if best_match:
                    column_mappings[missing_col] = best_match
                    print(f"🔍 数值智能匹配: '{missing_col}' -> '{best_match}'")
                else:
                    # 尝试关键词匹配
                    best_match = find_best_column_match(data.columns, missing_col)
                    if best_match:
                        column_mappings[missing_col] = best_match
                        print(f"🔍 关键词匹配: '{missing_col}' -> '{best_match}'")

    # 验证映射结果
    validation_results = validate_column_mapping(data, column_mappings)
    print_validation_results(validation_results)

    return column_mappings

def create_record_key(row_data):
    """
    创建记录的唯一标识键
    基于：广告账户 + 时间 + 广告名称
    """
    try:
        account = str(row_data.get('广告账户', '')).strip()
        time_val = str(row_data.get('时间', '')).strip()
        ad_name = str(row_data.get('广告名称', '')).strip()

        # 标准化时间格式
        if time_val and time_val != 'nan':
            try:
                # 尝试解析并标准化日期格式
                if '/' in time_val:
                    parts = time_val.split('/')
                    if len(parts) == 3:
                        year, month, day = parts
                        time_val = f"{year}/{int(month)}/{int(day)}"
            except:
                pass

        key = f"{account}|{time_val}|{ad_name}"
        return key
    except Exception as e:
        print(f"创建记录键时出错: {e}")
        return ""

def read_existing_data(worksheet):
    """
    读取工作表中的现有数据，创建索引
    返回：{record_key: row_number} 的字典
    """
    existing_data = {}

    try:
        # 获取表头信息
        headers = {}
        for col in range(1, worksheet.max_column + 1):
            header = worksheet.cell(row=1, column=col).value
            if header:
                headers[header] = col

        print(f"读取现有数据，表头: {list(headers.keys())}")

        # 读取数据行
        for row in range(2, worksheet.max_row + 1):
            row_data = {}

            # 读取关键字段
            for field in ['广告账户', '时间', '广告名称']:
                if field in headers:
                    col_idx = headers[field]
                    cell_value = worksheet.cell(row=row, column=col_idx).value
                    row_data[field] = cell_value

            # 创建记录键
            record_key = create_record_key(row_data)
            if record_key and record_key != "||":  # 确保不是空键
                existing_data[record_key] = row

        print(f"读取到 {len(existing_data)} 条现有记录")
        return existing_data

    except Exception as e:
        print(f"读取现有数据时出错: {e}")
        traceback.print_exc()
        return {}

def fast_merge_data_to_sheet(workbook, data_list, sheet_name):
    """
    高效版本：快速合并数据到工作表
    专注于台账处理，移除复杂逻辑
    """
    if not data_list:
        return 0

    try:
        worksheet = workbook[sheet_name]

        # 快速获取表头映射
        headers = {}
        for col in range(1, 17):  # 只检查前16列
            header = worksheet.cell(row=1, column=col).value
            if header:
                headers[header] = col

        # 快速读取现有数据（只读关键字段）
        existing_data = {}
        last_row = find_last_data_row(worksheet)

        for row in range(2, last_row + 1):
            account = worksheet.cell(row=row, column=headers.get('广告账户', 1)).value
            time_val = worksheet.cell(row=row, column=headers.get('时间', 2)).value
            ad_name = worksheet.cell(row=row, column=headers.get('广告名称', 5)).value

            key = f"{account}|{time_val}|{ad_name}"
            existing_data[key] = row

        # 定义需要更新的数值字段
        numeric_fields = ['展示次数', '点击量（全部）', '成效', '已花费金额 (USD)']

        # 公式模板
        formula_templates = {
            '广告系列': '=VLOOKUP(E{row},Sheet2!C:E,2,0)',
            '广告组': '=VLOOKUP(E{row},Sheet2!C:E,3,0)',
            '短剧编号': '=VLOOKUP(E{row},唯一广告名称!A:B,2,0)',
            '千次展示费用 (USD)': '=IFERROR(P{row}/G{row}*1000,0)',
            '点击注册转化率': '=IFERROR(K{row}/I{row},0)',
            '展示注册转化率': '=IFERROR(K{row}/G{row},0)',
            '点击率': '=I{row}/G{row}',
            '单次注册成本': '=IFERROR(P{row}/K{row},0)'
        }

        rows_updated = 0
        rows_added = 0

        for row_data in data_list:
            record_key = create_record_key(row_data)

            if record_key in existing_data:
                # 更新现有记录的数值字段
                existing_row = existing_data[record_key]
                for field in numeric_fields:
                    if field in row_data and field in headers:
                        col_idx = headers[field]
                        value = row_data[field]
                        if field == '已花费金额 (USD)':
                            value = clean_currency_value(value)
                        if value is not None:
                            worksheet.cell(row=existing_row, column=col_idx, value=value)
                rows_updated += 1
            else:
                # 添加新记录
                current_row = last_row + 1 + rows_added

                # 添加基础数据
                for target_col, value in row_data.items():
                    if target_col in headers and target_col not in formula_templates:
                        col_idx = headers[target_col]
                        if target_col == '已花费金额 (USD)':
                            value = clean_currency_value(value)
                        worksheet.cell(row=current_row, column=col_idx, value=value)

                # 添加公式
                for target_col, formula_template in formula_templates.items():
                    if target_col in headers:
                        col_idx = headers[target_col]
                        formula = formula_template.format(row=current_row)
                        if not formula.startswith('='):
                            formula = '=' + formula
                        worksheet.cell(row=current_row, column=col_idx, value=formula)

                rows_added += 1

        print(f"快速合并完成: 更新 {rows_updated} 行，新增 {rows_added} 行")
        return rows_updated + rows_added

    except Exception as e:
        print(f"快速合并出错: {e}")
        return 0

def merge_data_to_sheet(workbook, data_list, sheet_name, start_row=None):
    """
    兼容性函数，调用快速版本
    """
    # start_row参数保留以兼容旧代码，但在快速版本中不使用
    return fast_merge_data_to_sheet(workbook, data_list, sheet_name)

def append_data_to_sheet(workbook, data_list, sheet_name, start_row):
    """
    保留原函数以兼容性，但内部调用新的合并函数
    """
    return merge_data_to_sheet(workbook, data_list, sheet_name, start_row)

if __name__ == "__main__":
    # 源数据目录
    source_dir = r'C:\Users\<USER>\Desktop\juadge_data_deal\Data\origin_data'
    # 目标Excel文件
    target_excel = r'C:\Users\<USER>\Desktop\juadge_data_deal\Data\deal_data\广告数据对比总表.xlsx'
    
    try:
        # 处理文件
        print("开始处理文件...")
        process_files_to_excel(source_dir, target_excel)
        print("处理完成")
    except Exception as e:
        print(f"脚本执行时出错: {e}")
        traceback.print_exc()  # 打印详细错误 