#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查完整的新写入数据
"""

from openpyxl import load_workbook

# 加载工作簿
wb = load_workbook('juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx')
ws = wb['台账']

print("=== 检查新写入的完整数据 ===")

# 显示列标题
print("列标题:")
headers = []
for i in range(1, 17):  # 显示前16列
    headers.append(f"{chr(64+i)}:{ws.cell(1, i).value}")
print(headers)

print(f"\n新写入的数据（第501-512行）:")
for row_num in range(501, 513):
    print(f"\n第{row_num}行:")
    row_data = []
    for col_num in range(1, 17):  # 显示前16列
        cell_value = ws.cell(row_num, col_num).value
        row_data.append(f"{chr(64+col_num)}:{cell_value}")
    
    # 只显示非空行
    if any(ws.cell(row_num, col).value is not None for col in range(1, 17)):
        for item in row_data:
            print(f"  {item}")

print("\n=== 检查完成 ===")
