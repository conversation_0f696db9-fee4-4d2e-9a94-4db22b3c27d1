#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据写入结果
"""

from openpyxl import load_workbook

# 加载工作簿
wb = load_workbook('juadge_data_deal/Data/deal_data/广告数据对比总表.xlsx')
ws = wb['台账']

print("=== 数据验证结果 ===")
print(f"工作表最大行数: {ws.max_row}")
print(f"工作表最大列数: {ws.max_column}")

# 检查第1000-1012行的数据（根据日志，数据应该写入到这些行）
print("\n检查第1000-1012行的数据:")
for i in range(1000, 1012):
    row_data = []
    for j in range(1, 8):  # 检查前7列
        cell_value = ws.cell(i, j).value
        row_data.append(cell_value)
    
    # 只显示非空行
    if any(val is not None for val in row_data):
        print(f"第{i}行: {row_data}")

print("\n=== 验证完成 ===")
